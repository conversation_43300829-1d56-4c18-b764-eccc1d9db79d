import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { SkillsManager } from '../../../lib/skills-utils';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// Initialize skills manager
const skillsManager = new SkillsManager();

export async function GET(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get organizationId from query params
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    // Get organization skills
    const skills = await skillsManager.getOrganizationSkills(organizationId);

    return NextResponse.json({ skills });
  } catch (error) {
    console.error('Error in GET /api/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { organizationId, name, description, category } = body;

    // Validate required fields
    if (!organizationId || !name) {
      return NextResponse.json(
        { error: 'Organization ID and skill name are required' },
        { status: 400 }
      );
    }

    // Create new skill
    const skill = await skillsManager.createSkill(
      organizationId,
      name,
      description,
      category
    );

    return NextResponse.json(
      { skill },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/skills:', error);
    
    // Handle known errors
    if (error instanceof Error && error.message === 'Skill already exists in catalog') {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { id, isActive, description, category } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Skill ID is required' },
        { status: 400 }
      );
    }

    // Update skill
    const updatedSkill = await prisma.skillsCatalog.update({
      where: { id },
      data: {
        isActive: isActive !== undefined ? isActive : undefined,
        description: description !== undefined ? description : undefined,
        category: category !== undefined ? category : undefined
      }
    });

    return NextResponse.json({ skill: updatedSkill });
  } catch (error) {
    console.error('Error in PATCH /api/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get skill ID from query params
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Skill ID is required' },
        { status: 400 }
      );
    }

    // Delete skill (this will cascade to related records due to our schema setup)
    await prisma.skillsCatalog.delete({
      where: { id }
    });

    return NextResponse.json(
      { message: 'Skill deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}