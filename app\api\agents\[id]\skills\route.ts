import { NextRequest, NextResponse } from 'next/server';
import { SkillsManager } from '@/lib/skills-utils';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

const skillsManager = new SkillsManager();

interface Props {
  params: { id: string }
}

export async function GET(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Get agent skills
    const skills = await skillsManager.getAgentSkills(agentId);

    return NextResponse.json({ skills });
  } catch (error) {
    console.error('Error in GET /api/agents/[id]/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { skillName, proficiencyLevel } = body;

    if (!skillName) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    // Add skill to agent
    const skill = await skillsManager.addSkillToAgent(
      agentId,
      skillName,
      proficiencyLevel
    );

    return NextResponse.json(
      { skill },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/agents/[id]/skills:', error);
    
    // Handle known errors
    if (error instanceof Error) {
      if (error.message === 'Skill not found in catalog') {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      // Add other specific error cases as needed
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { skillName, proficiencyLevel } = body;

    if (!skillName || proficiencyLevel === undefined) {
      return NextResponse.json(
        { error: 'Skill name and proficiency level are required' },
        { status: 400 }
      );
    }

    // Update agent's skill level
    const updatedSkill = await skillsManager.updateAgentSkillLevel(
      agentId,
      skillName,
      proficiencyLevel
    );

    return NextResponse.json({ skill: updatedSkill });
  } catch (error) {
    console.error('Error in PATCH /api/agents/[id]/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Get skill name from query params
    const { searchParams } = new URL(req.url);
    const skillName = searchParams.get('skillName');

    if (!skillName) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    // Remove skill from agent
    await skillsManager.removeSkillFromAgent(agentId, skillName);

    return NextResponse.json(
      { message: 'Skill removed successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/agents/[id]/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}