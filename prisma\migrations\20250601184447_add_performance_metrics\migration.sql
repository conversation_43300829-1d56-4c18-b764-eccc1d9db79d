/*
  Warnings:

  - You are about to drop the `agent_performance` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "agent_performance" DROP CONSTRAINT "agent_performance_agent_id_fkey";

-- DropTable
DROP TABLE "agent_performance";

-- CreateTable
CREATE TABLE "agent_metrics" (
    "id" TEXT NOT NULL,
    "agent_id" TEXT NOT NULL,
    "period" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "tasks_assigned" INTEGER NOT NULL DEFAULT 0,
    "tasks_completed" INTEGER NOT NULL DEFAULT 0,
    "tasks_escalated" INTEGER NOT NULL DEFAULT 0,
    "total_work_time" INTEGER NOT NULL DEFAULT 0,
    "active_time" INTEGER NOT NULL DEFAULT 0,
    "avg_response_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "avg_handle_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "completion_rate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "quality_score" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "sla_compliance_rate" DOUBLE PRECISION NOT NULL DEFAULT 0,

    CONSTRAINT "agent_metrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "task_metrics" (
    "id" TEXT NOT NULL,
    "task_id" TEXT NOT NULL,
    "assigned_agent_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "assigned_at" TIMESTAMP(3),
    "first_response_at" TIMESTAMP(3),
    "resolved_at" TIMESTAMP(3),
    "wait_time" INTEGER,
    "handle_time" INTEGER,
    "total_time" INTEGER,
    "sla_policy" TEXT,
    "response_target" INTEGER,
    "resolve_target" INTEGER,
    "met_response_sla" BOOLEAN,
    "met_resolve_sla" BOOLEAN,
    "complexity" INTEGER,
    "quality" INTEGER,
    "customer_rating" INTEGER,

    CONSTRAINT "task_metrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "team_metrics" (
    "id" TEXT NOT NULL,
    "organization_id" TEXT NOT NULL,
    "period" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "total_tasks" INTEGER NOT NULL DEFAULT 0,
    "completed_tasks" INTEGER NOT NULL DEFAULT 0,
    "escalated_tasks" INTEGER NOT NULL DEFAULT 0,
    "avg_response_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "avg_handle_time" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "sla_compliance_rate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "customer_satisfaction" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "active_agents" INTEGER NOT NULL DEFAULT 0,
    "utilization_rate" DOUBLE PRECISION NOT NULL DEFAULT 0,

    CONSTRAINT "team_metrics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "agent_metrics_agent_id_period_idx" ON "agent_metrics"("agent_id", "period");

-- CreateIndex
CREATE UNIQUE INDEX "agent_metrics_agent_id_period_start_date_key" ON "agent_metrics"("agent_id", "period", "start_date");

-- CreateIndex
CREATE UNIQUE INDEX "task_metrics_task_id_key" ON "task_metrics"("task_id");

-- CreateIndex
CREATE INDEX "task_metrics_task_id_idx" ON "task_metrics"("task_id");

-- CreateIndex
CREATE INDEX "task_metrics_assigned_agent_id_idx" ON "task_metrics"("assigned_agent_id");

-- CreateIndex
CREATE INDEX "team_metrics_organization_id_period_idx" ON "team_metrics"("organization_id", "period");

-- CreateIndex
CREATE UNIQUE INDEX "team_metrics_organization_id_period_start_date_key" ON "team_metrics"("organization_id", "period", "start_date");

-- AddForeignKey
ALTER TABLE "agent_metrics" ADD CONSTRAINT "agent_metrics_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task_metrics" ADD CONSTRAINT "task_metrics_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task_metrics" ADD CONSTRAINT "task_metrics_assigned_agent_id_fkey" FOREIGN KEY ("assigned_agent_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "team_metrics" ADD CONSTRAINT "team_metrics_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
