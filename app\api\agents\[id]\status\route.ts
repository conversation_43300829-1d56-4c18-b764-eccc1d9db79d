import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { UserStatus } from '@prisma/client'

// PATCH /api/agents/[id]/status - Update agent availability status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    
    // Validate status
    if (!body.status || !Object.values(UserStatus).includes(body.status)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_STATUS',
            message: `Invalid status. Must be one of: ${Object.values(UserStatus).join(', ')}`,
          },
        },
        { status: 400 }
      )
    }

    // Check if agent exists
    const existingAgent = await prisma.user.findUnique({
      where: { id: params.id },
    })

    if (!existingAgent) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AGENT_NOT_FOUND',
            message: 'Agent not found',
          },
        },
        { status: 404 }
      )
    }

    // Update agent status
    const updatedAgent = await prisma.user.update({
      where: { id: params.id },
      data: { 
        status: body.status,
        updatedAt: new Date(),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            assignedTasks: true,
          },
        },
      },
    })

    // If agent is going offline/away, we might want to handle task reassignment
    // For now, we'll just log this for future implementation
    if (body.status === UserStatus.OFFLINE || body.status === UserStatus.AWAY) {
      console.log(`Agent ${updatedAgent.name} is now ${body.status}. Consider task reassignment.`)
    }

    return NextResponse.json({
      success: true,
      data: updatedAgent,
      message: `Agent status updated to ${body.status}`,
    })

  } catch (error) {
    console.error('Error updating agent status:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'UPDATE_STATUS_FAILED',
          message: 'Failed to update agent status',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}

// GET /api/agents/[id]/status - Get agent status and availability info
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const agent = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        name: true,
        email: true,
        status: true,
        currentTaskCount: true,
        maxConcurrentTasks: true,
        updatedAt: true,
      },
    })

    if (!agent) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AGENT_NOT_FOUND',
            message: 'Agent not found',
          },
        },
        { status: 404 }
      )
    }

    // Calculate availability metrics
    const utilizationRate = agent.maxConcurrentTasks > 0 
      ? (agent.currentTaskCount / agent.maxConcurrentTasks) * 100 
      : 0

    const isAvailable = agent.status === UserStatus.AVAILABLE && 
                       agent.currentTaskCount < agent.maxConcurrentTasks

    const availabilityInfo = {
      ...agent,
      utilizationRate,
      isAvailable,
      availableCapacity: Math.max(0, agent.maxConcurrentTasks - agent.currentTaskCount),
      statusLastUpdated: agent.updatedAt,
    }

    return NextResponse.json({
      success: true,
      data: availabilityInfo,
    })

  } catch (error) {
    console.error('Error fetching agent status:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'FETCH_STATUS_FAILED',
          message: 'Failed to fetch agent status',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}
