import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getDemoOrgId } from "@/lib/db-utils";

// GET /api/agents/[id]/performance - Get agent performance metrics
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const organizationId = await getDemoOrgId();

    // Check if agent exists and belongs to organization
    const agent = await prisma.user.findFirst({
      where: {
        id: params.id,
        organizationId,
      },
      include: {
        performanceMetrics: true,
      },
    });

    if (!agent) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "AGENT_NOT_FOUND",
            message: "Agent not found",
          },
        },
        { status: 404 }
      );
    }

    // If no performance data exists, create default
    if (!agent.performanceMetrics || agent.performanceMetrics.length === 0) {
      const defaultPerformance = await prisma.performanceMetric.create({
        data: {
          agentId: params.id,
          organizationId,
          metricType: "current",
          periodStart: new Date(),
          periodEnd: new Date(),
          tasksCompleted: 0,
          tasksAssigned: 0,
          avgResponseTime: 0,
          avgResolutionTime: 0,
          qualityScore: 0.0,
          customerRating: 0.0,
          slaResponseMet: 0,
          slaResolutionMet: 0,
          slaResponseTotal: 0,
          slaResolutionTotal: 0,
        },
      });

      return NextResponse.json({
        success: true,
        data: {
          agentId: params.id,
          agentName: agent.name,
          performance: defaultPerformance,
        },
        message: "Default performance metrics created",
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        agentId: params.id,
        agentName: agent.name,
        performance: agent.performanceMetrics[0] || null,
      },
      message: "Performance metrics retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching agent performance:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "FETCH_PERFORMANCE_FAILED",
          message: "Failed to fetch agent performance",
        },
      },
      { status: 500 }
    );
  }
}

// POST /api/agents/[id]/performance - Update agent performance metrics
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const {
      avgResponseTime,
      avgResolutionTime,
      completionRate,
      qualityScore,
      totalTasksCompleted,
      totalTasksAssigned,
    } = body;

    const organizationId = await getDemoOrgId();

    // Check if agent exists and belongs to organization
    const agent = await prisma.user.findFirst({
      where: {
        id: params.id,
        organizationId,
      },
    });

    if (!agent) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "AGENT_NOT_FOUND",
            message: "Agent not found",
          },
        },
        { status: 404 }
      );
    }

    // Validate input data
    if (
      completionRate !== undefined &&
      (completionRate < 0 || completionRate > 1)
    ) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "INVALID_COMPLETION_RATE",
            message: "Completion rate must be between 0 and 1",
          },
        },
        { status: 400 }
      );
    }

    if (
      qualityScore !== undefined &&
      (qualityScore < 0 || qualityScore > 100)
    ) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "INVALID_QUALITY_SCORE",
            message: "Quality score must be between 0 and 100",
          },
        },
        { status: 400 }
      );
    }

    // Update or create performance record
    const updatedPerformance = await prisma.performanceMetric.upsert({
      where: {
        agentId_metricType_periodStart: {
          agentId: params.id,
          metricType: "current",
          periodStart: new Date(new Date().toDateString()), // Start of today
        },
      },
      update: {
        ...(avgResponseTime !== undefined && { avgResponseTime }),
        ...(avgResolutionTime !== undefined && { avgResolutionTime }),
        ...(qualityScore !== undefined && { qualityScore }),
        ...(totalTasksCompleted !== undefined && {
          tasksCompleted: totalTasksCompleted,
        }),
        ...(totalTasksAssigned !== undefined && {
          tasksAssigned: totalTasksAssigned,
        }),
        periodEnd: new Date(),
      },
      create: {
        agentId: params.id,
        organizationId,
        metricType: "current",
        periodStart: new Date(new Date().toDateString()),
        periodEnd: new Date(),
        avgResponseTime: avgResponseTime || 0,
        avgResolutionTime: avgResolutionTime || 0,
        qualityScore: qualityScore || 0.0,
        customerRating: 0.0,
        tasksCompleted: totalTasksCompleted || 0,
        tasksAssigned: totalTasksAssigned || 0,
        slaResponseMet: 0,
        slaResolutionMet: 0,
        slaResponseTotal: 0,
        slaResolutionTotal: 0,
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        agentId: params.id,
        performance: updatedPerformance,
      },
      message: "Performance metrics updated successfully",
    });
  } catch (error) {
    console.error("Error updating agent performance:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "UPDATE_PERFORMANCE_FAILED",
          message: "Failed to update agent performance",
        },
      },
      { status: 500 }
    );
  }
}

// PATCH /api/agents/[id]/performance - Increment performance metrics (for task completion)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { taskCompleted, responseTime, resolutionTime, qualityRating } = body;

    const organizationId = await getDemoOrgId();

    // Check if agent exists
    const agent = await prisma.user.findFirst({
      where: {
        id: params.id,
        organizationId,
      },
      include: {
        performanceMetrics: true,
      },
    });

    if (!agent) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "AGENT_NOT_FOUND",
            message: "Agent not found",
          },
        },
        { status: 404 }
      );
    }

    // Get current performance or create default
    const currentPerf = agent.performanceMetrics?.[0] || {
      avgResponseTime: 0,
      avgResolutionTime: 0,
      qualityScore: 0.0,
      tasksCompleted: 0,
      tasksAssigned: 0,
    };

    // Calculate new metrics
    const newTotalCompleted = taskCompleted
      ? currentPerf.tasksCompleted + 1
      : currentPerf.tasksCompleted;
    const newTotalAssigned = currentPerf.tasksAssigned + 1;

    // Calculate new averages
    const newAvgResponseTime = responseTime
      ? (currentPerf.avgResponseTime * currentPerf.tasksCompleted +
          responseTime) /
        newTotalCompleted
      : currentPerf.avgResponseTime;

    const newAvgResolutionTime =
      resolutionTime && taskCompleted
        ? (currentPerf.avgResolutionTime * currentPerf.tasksCompleted +
            resolutionTime) /
          newTotalCompleted
        : currentPerf.avgResolutionTime;

    const newQualityScore =
      qualityRating && taskCompleted
        ? (currentPerf.qualityScore * currentPerf.tasksCompleted +
            qualityRating) /
          newTotalCompleted
        : currentPerf.qualityScore;

    // Update performance
    const updatedPerformance = await prisma.performanceMetric.upsert({
      where: {
        agentId_metricType_periodStart: {
          agentId: params.id,
          metricType: "current",
          periodStart: new Date(new Date().toDateString()),
        },
      },
      update: {
        avgResponseTime: Math.round(newAvgResponseTime),
        avgResolutionTime: Math.round(newAvgResolutionTime),
        qualityScore: newQualityScore,
        tasksCompleted: newTotalCompleted,
        tasksAssigned: newTotalAssigned,
        periodEnd: new Date(),
      },
      create: {
        agentId: params.id,
        organizationId,
        metricType: "current",
        periodStart: new Date(new Date().toDateString()),
        periodEnd: new Date(),
        avgResponseTime: Math.round(responseTime || 0),
        avgResolutionTime: Math.round(resolutionTime || 0),
        qualityScore: qualityRating || 0.0,
        customerRating: 0.0,
        tasksCompleted: taskCompleted ? 1 : 0,
        tasksAssigned: 1,
        slaResponseMet: 0,
        slaResolutionMet: 0,
        slaResponseTotal: 0,
        slaResolutionTotal: 0,
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        agentId: params.id,
        performance: updatedPerformance,
        metrics: {
          taskCompleted,
          responseTime,
          resolutionTime,
          qualityRating,
        },
      },
      message: "Performance metrics incremented successfully",
    });
  } catch (error) {
    console.error("Error incrementing agent performance:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "INCREMENT_PERFORMANCE_FAILED",
          message: "Failed to increment agent performance",
        },
      },
      { status: 500 }
    );
  }
}
