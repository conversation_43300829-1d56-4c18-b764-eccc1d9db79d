import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { EnhancedTaskRouter } from "@/lib/enhanced-task-router";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { TaskMetadata } from "@/lib/types";
import { TaskStatus, TaskEventType, Prisma } from "@prisma/client";

const router = new EnhancedTaskRouter();

interface Props {
  params: { id: string };
}

export async function POST(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const taskId = params.id;

    // Get strategy from request body (optional)
    const body = await req.json();
    const { strategy } = body;

    // Get task with required skills and escalations
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        requiredSkills: true,
        escalations: true,
      },
    });

    if (!task) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 });
    }

    if (task.assignedTo) {
      return NextResponse.json(
        { error: "Task is already assigned" },
        { status: 400 }
      );
    }

    // Route task using enhanced router
    const result = await router.routeTask({
      task,
      strategy,
    });

    const metadata: TaskMetadata = {
      ...(task.metadata as TaskMetadata),
      assignmentDetails: {
        confidence: result.confidence,
        reasoning: Array.isArray(result.reasoning)
          ? result.reasoning.join("; ")
          : result.reasoning,
        strategy: result.appliedStrategy || strategy || "weighted_round_robin",
        alternativeAgents: result.alternativeAgents.map((a) => a.id),
      },
      appliedRules: result.ruleResults?.map((r) => ({
        ruleId: r.ruleId,
        ruleName: r.ruleName,
        succeeded: r.succeeded,
        conditionsMatched: r.conditionsMatched,
        actionsExecuted: r.actionsExecuted,
      })),
    };

    // Update task assignment
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: {
        assignedTo: result.selectedAgent.id,
        assignedAt: new Date(),
        status: TaskStatus.ASSIGNED,
        metadata: metadata as Prisma.InputJsonValue,
      },
      include: {
        assignedUser: {
          select: {
            id: true,
            name: true,
            email: true,
            currentTaskCount: true,
          },
        },
      },
    });

    // Update agent's task count
    await prisma.user.update({
      where: { id: result.selectedAgent.id },
      data: {
        currentTaskCount: {
          increment: 1,
        },
      },
    });

    // Format rule results for event data
    const eventRuleResults = result.ruleResults?.map((r) => ({
      ruleId: r.ruleId,
      ruleName: r.ruleName,
      succeeded: r.succeeded,
      conditionsMatched: r.conditionsMatched,
      actionsExecuted: r.actionsExecuted,
    }));

    // Create task event for assignment
    await prisma.taskEvent.create({
      data: {
        taskId,
        type: TaskEventType.ASSIGNED,
        userId: result.selectedAgent.id,
        data: {
          confidence: result.confidence,
          reasoning: Array.isArray(result.reasoning)
            ? result.reasoning.join("; ")
            : result.reasoning,
          strategy:
            result.appliedStrategy || strategy || "weighted_round_robin",
          ruleResults: eventRuleResults || [],
        } as Prisma.InputJsonValue,
      },
    });

    return NextResponse.json({
      task: updatedTask,
      assignment: {
        agent: result.selectedAgent,
        confidence: result.confidence,
        reasoning: Array.isArray(result.reasoning)
          ? result.reasoning.join("; ")
          : result.reasoning,
        alternativeAgents: result.alternativeAgents,
        strategy: result.appliedStrategy || strategy || "weighted_round_robin",
        ruleResults: result.ruleResults,
      },
    });
  } catch (error) {
    console.error("Error in POST /api/tasks/[id]/assign:", error);

    if (
      error instanceof Error &&
      error.message === "No eligible agents available"
    ) {
      return NextResponse.json(
        { error: "No eligible agents available for assignment" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const taskId = params.id;

    // Get task
    const task = await prisma.task.findUnique({
      where: { id: taskId },
    });

    if (!task) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 });
    }

    if (!task.assignedTo) {
      return NextResponse.json(
        { error: "Task is not assigned" },
        { status: 400 }
      );
    }

    const metadata: TaskMetadata = {
      ...(task.metadata as TaskMetadata),
      unassignmentDetails: {
        unassignedAt: new Date(),
        reason: "manual_unassignment",
        previousAgent: task.assignedTo,
      },
    };

    // Update task
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: {
        assignedTo: null,
        assignedAt: null,
        status: TaskStatus.PENDING,
        metadata: metadata as Prisma.InputJsonValue,
      },
    });

    // Update agent's task count
    await prisma.user.update({
      where: { id: task.assignedTo },
      data: {
        currentTaskCount: {
          decrement: 1,
        },
      },
    });

    // Create task event for unassignment
    await prisma.taskEvent.create({
      data: {
        taskId,
        type: TaskEventType.STATUS_CHANGED,
        userId: task.assignedTo,
        data: {
          status: TaskStatus.PENDING,
          reason: "manual_unassignment",
        },
      },
    });

    return NextResponse.json({ task: updatedTask });
  } catch (error) {
    console.error("Error in DELETE /api/tasks/[id]/assign:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
