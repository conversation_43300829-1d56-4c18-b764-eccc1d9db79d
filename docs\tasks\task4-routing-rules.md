# Task 4: Routing Rules Engine

## 🎯 Objective

Implement a flexible routing rules engine that allows organizations to define custom business logic for task assignment based on conditions and actions.

## 📋 Status

- **Priority**: P1 (Critical)
- **Status**: ⏳ Waiting for Tasks 2 & 3
- **Estimated Time**: 8-10 hours
- **Dependencies**: Task 2 (Enhanced Routing), Task 3 (Working Hours)

## 🔧 Technical Requirements

### Database Schema Updates

```prisma
model RoutingRule {
  id             String   @id @default(cuid())
  organizationId String   @map("organization_id")
  name           String
  description    String?
  priority       Int      @default(0) // Higher number = higher priority
  isActive       Boolean  @default(true) @map("is_active")
  conditions     J<PERSON>     @default("[]") // Array of RuleCondition
  actions        Json     @default("[]") // Array of RuleAction
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@unique([organizationId, name])
  @@map("routing_rules")
}

model RuleExecution {
  id           String   @id @default(cuid())
  ruleId       String   @map("rule_id")
  taskId       String   @map("task_id")
  executedAt   DateTime @default(now()) @map("executed_at")
  success      Boolean
  resultData   Json?    @map("result_data")
  errorMessage String?  @map("error_message")
  
  rule RoutingRule @relation(fields: [ruleId], references: [id], onDelete: Cascade)
  task Task        @relation(fields: [taskId], references: [id], onDelete: Cascade)
  
  @@map("rule_executions")
}
```

### TypeScript Types

```typescript
export interface RuleCondition {
  field: string; // 'task.type', 'task.priority', 'task.source', 'agent.skills', 'time.hour', etc.
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in' | 'exists' | 'not_exists';
  value: any;
  logicalOperator?: 'AND' | 'OR'; // For chaining conditions
}

export interface RuleAction {
  type: 'assign_to_agent' | 'assign_to_team' | 'route_by_skill' | 'route_by_performance' | 'escalate' | 'set_priority' | 'add_tag' | 'send_notification';
  parameters: Record<string, any>;
}

export interface RoutingRuleDefinition {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  priority: number;
  isActive: boolean;
  conditions: RuleCondition[];
  actions: RuleAction[];
  createdAt: Date;
  updatedAt: Date;
}

export interface RuleExecutionResult {
  ruleId: string;
  success: boolean;
  actions: RuleAction[];
  resultData?: any;
  errorMessage?: string;
}
```

## 🛠️ Implementation Steps

### Step 1: Database Schema Updates (30 minutes)

1. **Update Prisma Schema**
   - Add RoutingRule and RuleExecution models
   - Update relations with Organization and Task models

2. **Create Migration**
   ```bash
   npx prisma migrate dev --name add-routing-rules-engine
   ```

3. **Update Seed Data**
   - Add sample routing rules for demo organization
   - Create common rule templates

### Step 2: Rules Engine Core (150 minutes)

1. **Create `lib/routing-rules-engine.ts`**
   ```typescript
   export class RoutingRulesEngine {
     async evaluateRules(task: TaskWithSkills, organizationId: string): Promise<RuleExecutionResult[]> {
       const rules = await this.getActiveRules(organizationId);
       const results: RuleExecutionResult[] = [];
       
       // Sort by priority (highest first)
       const sortedRules = rules.sort((a, b) => b.priority - a.priority);
       
       for (const rule of sortedRules) {
         try {
           const conditionsMet = await this.evaluateConditions(rule.conditions, task);
           
           if (conditionsMet) {
             const result = await this.executeActions(rule.actions, task);
             results.push({
               ruleId: rule.id,
               success: true,
               actions: rule.actions,
               resultData: result
             });
             
             // Log execution
             await this.logRuleExecution(rule.id, task.id, true, result);
             
             // Check if rule should stop further processing
             if (this.shouldStopProcessing(rule.actions)) {
               break;
             }
           }
         } catch (error) {
           results.push({
             ruleId: rule.id,
             success: false,
             actions: rule.actions,
             errorMessage: error.message
           });
           
           await this.logRuleExecution(rule.id, task.id, false, null, error.message);
         }
       }
       
       return results;
     }

     async evaluateConditions(conditions: RuleCondition[], task: TaskWithSkills): Promise<boolean> {
       if (conditions.length === 0) return true;
       
       let result = true;
       let currentLogicalOp = 'AND';
       
       for (const condition of conditions) {
         const conditionResult = await this.evaluateCondition(condition, task);
         
         if (currentLogicalOp === 'AND') {
           result = result && conditionResult;
         } else {
           result = result || conditionResult;
         }
         
         currentLogicalOp = condition.logicalOperator || 'AND';
       }
       
       return result;
     }

     async evaluateCondition(condition: RuleCondition, task: TaskWithSkills): Promise<boolean> {
       const fieldValue = await this.getFieldValue(condition.field, task);
       
       switch (condition.operator) {
         case 'equals':
           return fieldValue === condition.value;
         case 'not_equals':
           return fieldValue !== condition.value;
         case 'contains':
           return String(fieldValue).includes(String(condition.value));
         case 'not_contains':
           return !String(fieldValue).includes(String(condition.value));
         case 'greater_than':
           return Number(fieldValue) > Number(condition.value);
         case 'less_than':
           return Number(fieldValue) < Number(condition.value);
         case 'in':
           return Array.isArray(condition.value) && condition.value.includes(fieldValue);
         case 'not_in':
           return Array.isArray(condition.value) && !condition.value.includes(fieldValue);
         case 'exists':
           return fieldValue !== null && fieldValue !== undefined;
         case 'not_exists':
           return fieldValue === null || fieldValue === undefined;
         default:
           throw new Error(`Unknown operator: ${condition.operator}`);
       }
     }

     async getFieldValue(field: string, task: TaskWithSkills): Promise<any> {
       const [category, property] = field.split('.');
       
       switch (category) {
         case 'task':
           return this.getTaskProperty(task, property);
         case 'time':
           return this.getTimeProperty(property);
         case 'organization':
           return this.getOrganizationProperty(task.organizationId, property);
         default:
           throw new Error(`Unknown field category: ${category}`);
       }
     }

     async executeActions(actions: RuleAction[], task: TaskWithSkills): Promise<any> {
       const results = [];
       
       for (const action of actions) {
         const result = await this.executeAction(action, task);
         results.push(result);
       }
       
       return results;
     }

     async executeAction(action: RuleAction, task: TaskWithSkills): Promise<any> {
       switch (action.type) {
         case 'assign_to_agent':
           return this.assignToSpecificAgent(task, action.parameters.agentId);
         case 'assign_to_team':
           return this.assignToTeam(task, action.parameters.teamId);
         case 'route_by_skill':
           return this.routeBySkill(task, action.parameters);
         case 'route_by_performance':
           return this.routeByPerformance(task, action.parameters);
         case 'escalate':
           return this.escalateTask(task, action.parameters);
         case 'set_priority':
           return this.setPriority(task, action.parameters.priority);
         case 'add_tag':
           return this.addTag(task, action.parameters.tag);
         case 'send_notification':
           return this.sendNotification(task, action.parameters);
         default:
           throw new Error(`Unknown action type: ${action.type}`);
       }
     }
   }
   ```

### Step 3: Rule Condition Evaluators (90 minutes)

1. **Task Property Evaluators**
   ```typescript
   getTaskProperty(task: TaskWithSkills, property: string): any {
     switch (property) {
       case 'type':
         return task.type;
       case 'priority':
         return task.priority;
       case 'source':
         return task.source;
       case 'estimatedDuration':
         return task.estimatedDuration;
       case 'requiredSkills':
         return task.requiredSkills.map(rs => rs.skillName);
       case 'requiredSkillsCount':
         return task.requiredSkills.length;
       case 'title':
         return task.title;
       case 'description':
         return task.description;
       case 'metadata':
         return task.metadata;
       default:
         return task.metadata?.[property];
     }
   }
   ```

2. **Time Property Evaluators**
   ```typescript
   getTimeProperty(property: string): any {
     const now = new Date();
     
     switch (property) {
       case 'hour':
         return now.getHours();
       case 'dayOfWeek':
         return now.getDay();
       case 'isWeekend':
         return now.getDay() === 0 || now.getDay() === 6;
       case 'isBusinessHours':
         return this.isBusinessHours(now);
       case 'timestamp':
         return now.getTime();
       default:
         throw new Error(`Unknown time property: ${property}`);
     }
   }
   ```

### Step 4: Rule Action Executors (120 minutes)

1. **Assignment Actions**
   ```typescript
   async assignToSpecificAgent(task: TaskWithSkills, agentId: string): Promise<any> {
     // Check if agent is available and capable
     const agent = await prisma.user.findUnique({
       where: { id: agentId },
       include: { skills: true }
     });
     
     if (!agent) throw new Error('Agent not found');
     
     const workingHoursManager = new WorkingHoursManager();
     const isAvailable = await workingHoursManager.isAgentAvailable(agentId);
     
     if (!isAvailable) throw new Error('Agent not available');
     
     // Assign task
     await prisma.task.update({
       where: { id: task.id },
       data: {
         assignedTo: agentId,
         assignedAt: new Date(),
         status: 'assigned'
       }
     });
     
     return { agentId, assignedAt: new Date() };
   }

   async routeBySkill(task: TaskWithSkills, parameters: any): Promise<any> {
     const enhancedRouter = new EnhancedTaskRouter();
     const context: RoutingContext = {
       task,
       availableAgents: await enhancedRouter.getEligibleAgents(task),
       organizationId: task.organizationId,
       strategy: 'best_skill_match'
     };
     
     const result = await enhancedRouter.routeTask(context);
     
     // Apply assignment
     await prisma.task.update({
       where: { id: task.id },
       data: {
         assignedTo: result.selectedAgent.id,
         assignedAt: new Date(),
         status: 'assigned'
       }
     });
     
     return {
       agentId: result.selectedAgent.id,
       confidence: result.confidence,
       reasoning: result.reasoning
     };
   }
   ```

2. **Task Modification Actions**
   ```typescript
   async setPriority(task: TaskWithSkills, priority: string): Promise<any> {
     await prisma.task.update({
       where: { id: task.id },
       data: { priority }
     });
     
     return { oldPriority: task.priority, newPriority: priority };
   }

   async escalateTask(task: TaskWithSkills, parameters: any): Promise<any> {
     const escalationLevel = parameters.level || 1;
     const escalateTo = parameters.escalateTo;
     
     await prisma.task.update({
       where: { id: task.id },
       data: {
         priority: 'urgent',
         assignedTo: escalateTo,
         status: 'escalated',
         metadata: {
           ...task.metadata,
           escalationLevel,
           escalatedAt: new Date(),
           escalatedFrom: task.assignedTo
         }
       }
     });
     
     return { escalationLevel, escalatedTo: escalateTo };
   }
   ```

### Step 5: API Endpoints (90 minutes)

1. **Routing Rules CRUD** - `app/api/routing-rules/route.ts`
   ```typescript
   // GET: List organization routing rules
   // POST: Create new routing rule
   ```

2. **Individual Rule Management** - `app/api/routing-rules/[id]/route.ts`
   ```typescript
   // GET: Get rule details
   // PATCH: Update rule
   // DELETE: Delete rule
   ```

3. **Rule Testing** - `app/api/routing-rules/test/route.ts`
   ```typescript
   // POST: Test rule against sample task data
   ```

4. **Rule Execution History** - `app/api/routing-rules/executions/route.ts`
   ```typescript
   // GET: Get rule execution history and analytics
   ```

### Step 6: Integration with Task Router (60 minutes)

1. **Update Enhanced Task Router**
   ```typescript
   async routeTask(context: RoutingContext): Promise<RoutingResult> {
     // 1. First, apply routing rules
     const rulesEngine = new RoutingRulesEngine();
     const ruleResults = await rulesEngine.evaluateRules(context.task, context.organizationId);
     
     // Check if any rule resulted in assignment
     for (const result of ruleResults) {
       if (result.success && this.isAssignmentAction(result.actions)) {
         // Task was assigned by rule, return result
         return this.createResultFromRuleExecution(result);
       }
     }
     
     // 2. No rule assigned the task, use default routing
     return await this.defaultRouting(context);
   }
   ```

## 🧪 Testing Requirements

### Unit Tests
- Rule condition evaluation logic
- Rule action execution
- Field value extraction
- Logical operator handling

### Integration Tests
- End-to-end rule execution
- API endpoints functionality
- Integration with task routing
- Rule priority handling

### Manual Testing
- Create various rule types
- Test rule conditions with different tasks
- Verify rule actions execute correctly
- Test rule priority and stopping behavior

## 📝 Acceptance Criteria

- [ ] Organizations can create custom routing rules
- [ ] Rules support multiple condition types and operators
- [ ] Rules can execute various actions (assignment, escalation, etc.)
- [ ] Rule priority determines execution order
- [ ] Rule execution is logged for audit purposes
- [ ] Rules integrate seamlessly with existing routing engine
- [ ] API endpoints for rule management are functional
- [ ] Rule testing capability is available

## 🔗 Dependencies for Next Tasks

This task enables:
- **Task 5**: SLA escalation can use routing rules
- **Task 7**: UI for rule configuration and management

## 📋 Deliverables

1. Routing rules database schema
2. Rules engine with condition evaluation
3. Rule action execution system
4. API endpoints for rule management
5. Integration with enhanced task router
6. Rule execution logging and audit trail
7. Rule testing capabilities
8. Comprehensive unit tests

**Next Task**: [Task 5: SLA & Escalation System](./task5-sla-escalation.md)
