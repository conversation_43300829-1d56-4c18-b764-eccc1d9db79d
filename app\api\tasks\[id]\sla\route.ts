import { NextRequest, NextResponse } from 'next/server';
import { SLAManager } from '@/lib/sla-manager';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

const slaManager = new SLAManager();

interface Props {
  params: { id: string }
}

export async function GET(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Get task's SLA status
    const status = await slaManager.checkSLAStatus(taskId);

    return NextResponse.json({ status });
  } catch (error) {
    console.error('Error in GET /api/tasks/[id]/sla:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { policyId } = body;

    if (!policyId) {
      return NextResponse.json(
        { error: 'SLA Policy ID is required' },
        { status: 400 }
      );
    }

    // Apply SLA policy to task
    const task = await slaManager.applySLAPolicy(taskId, policyId);

    return NextResponse.json({ task });
  } catch (error) {
    console.error('Error in POST /api/tasks/[id]/sla:', error);
    
    if (error instanceof Error && error.message === 'SLA Policy not found') {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { action } = body;

    if (!action || !['respond', 'resolve'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action' },
        { status: 400 }
      );
    }

    // Update task status
    const task = action === 'respond'
      ? await slaManager.markResponded(taskId)
      : await slaManager.markResolved(taskId);

    return NextResponse.json({ task });
  } catch (error) {
    console.error('Error in PATCH /api/tasks/[id]/sla:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}