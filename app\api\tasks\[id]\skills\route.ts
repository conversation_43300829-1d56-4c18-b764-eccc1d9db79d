import { NextRequest, NextResponse } from 'next/server';
import { SkillsManager } from '@/lib/skills-utils';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

const skillsManager = new SkillsManager();

interface Props {
  params: { id: string }
}

export async function GET(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Get task skill requirements
    const skills = await skillsManager.getTaskSkillRequirements(taskId);

    return NextResponse.json({ skills });
  } catch (error) {
    console.error('Error in GET /api/tasks/[id]/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { skillName, requiredLevel } = body;

    if (!skillName) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    // Add skill requirement to task
    const skill = await skillsManager.addSkillRequirementToTask(
      taskId,
      skillName,
      requiredLevel
    );

    return NextResponse.json(
      { skill },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/tasks/[id]/skills:', error);
    
    // Handle known errors
    if (error instanceof Error) {
      if (error.message === 'Skill not found in catalog') {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { skillName, requiredLevel } = body;

    if (!skillName || requiredLevel === undefined) {
      return NextResponse.json(
        { error: 'Skill name and required level are required' },
        { status: 400 }
      );
    }

    // Update skill requirement
    const updatedSkill = await prisma.taskRequiredSkill.update({
      where: {
        taskId_skillName: {
          taskId,
          skillName
        }
      },
      data: {
        requiredLevel: Math.min(Math.max(requiredLevel, 1), 5) // Ensure 1-5 range
      }
    });

    return NextResponse.json({ skill: updatedSkill });
  } catch (error) {
    console.error('Error in PATCH /api/tasks/[id]/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Get skill name from query params
    const { searchParams } = new URL(req.url);
    const skillName = searchParams.get('skillName');

    if (!skillName) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    // Remove skill requirement from task
    await skillsManager.removeSkillRequirementFromTask(taskId, skillName);

    return NextResponse.json(
      { message: 'Skill requirement removed successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/tasks/[id]/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}