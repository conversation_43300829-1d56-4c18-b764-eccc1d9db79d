import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // If user is not authenticated and trying to access protected routes
    if (!token && (pathname.startsWith('/admin') || pathname.startsWith('/agent'))) {
      return NextResponse.redirect(new URL('/auth/signin', req.url))
    }

    // If user is authenticated
    if (token) {
      // Redirect admin users away from agent routes
      if (token.role === 'ADMIN' && pathname.startsWith('/agent')) {
        return NextResponse.redirect(new URL('/admin', req.url))
      }

      // Redirect agent users away from admin routes
      if (token.role === 'AGENT' && pathname.startsWith('/admin')) {
        return NextResponse.redirect(new URL('/agent', req.url))
      }

      // Redirect authenticated users away from auth pages
      if (pathname.startsWith('/auth/signin')) {
        if (token.role === 'ADMIN') {
          return NextResponse.redirect(new URL('/admin', req.url))
        } else {
          return NextResponse.redirect(new URL('/agent', req.url))
        }
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl
        
        // Allow access to public routes
        if (pathname === '/' || pathname.startsWith('/auth') || pathname.startsWith('/api/auth') || pathname.startsWith('/dashboard')) {
          return true
        }

        // Require authentication for protected routes
        if (pathname.startsWith('/admin') || pathname.startsWith('/agent')) {
          return !!token
        }

        return true
      },
    },
  }
)

export const config = {
  matcher: [
    '/admin/:path*',
    '/agent/:path*',
    '/auth/:path*',
  ]
}
