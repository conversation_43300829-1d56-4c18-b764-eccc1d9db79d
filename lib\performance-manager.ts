import { prisma } from './prisma';
import {
  AgentMetrics,
  TaskMetrics,
  TeamMetrics,
  MetricPeriod,
  Task,
  User
} from './types';

export class PerformanceManager {
  private metricsCollector: MetricsCollector;

  constructor() {
    this.metricsCollector = new MetricsCollector();
  }

  // Real-time event handlers
  async onTaskCompleted(taskId: string): Promise<void> {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        metrics: true,
        slaPolicy: true,
        escalations: true
      }
    });

    if (!task || !task.assignedTo) {
      throw new Error('Task not found or not assigned');
    }

    // Update task metrics
    await this.updateTaskMetrics(taskId);

    // Trigger real-time metrics collection
    await this.metricsCollector.onTaskCompleted(taskId, task.assignedTo);
  }

  async onTaskAssigned(taskId: string, agentId: string): Promise<void> {
    // Update task metrics
    await this.updateTaskMetrics(taskId);

    // Trigger real-time routing metrics update
    await this.metricsCollector.onTaskAssigned(taskId, agentId);

    // Update agent metrics for new assignment
    await this.updateAgentMetrics(agentId);
  }

  // Task Metrics Management
  async updateTaskMetrics(taskId: string): Promise<TaskMetrics> {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        metrics: true,
        slaPolicy: true,
        escalations: true
      }
    });

    if (!task) {
      throw new Error('Task not found');
    }

    // Calculate metrics
    const metrics = this.calculateTaskMetrics(task);

    // Update or create task metrics
    return await prisma.taskMetrics.upsert({
      where: {
        taskId
      },
      update: metrics,
      create: {
        taskId,
        ...metrics
      }
    });
  }

  // Agent Metrics Management
  async updateAgentMetrics(
    agentId: string,
    period: MetricPeriod = 'daily'
  ): Promise<AgentMetrics> {
    const timeRange = this.getTimeRange(period);
    
    // Get tasks for the period
    const tasks = await prisma.task.findMany({
      where: {
        assignedTo: agentId,
        createdAt: {
          gte: timeRange.startDate,
          lt: timeRange.endDate
        }
      },
      include: {
        metrics: true,
        escalations: true
      }
    });

    // Calculate metrics
    const metrics = this.calculateAgentMetrics(tasks);

    // Update or create agent metrics
    return await prisma.agentMetrics.upsert({
      where: {
        agentId_period_startDate: {
          agentId,
          period,
          startDate: timeRange.startDate
        }
      },
      update: metrics,
      create: {
        agentId,
        period,
        startDate: timeRange.startDate,
        endDate: timeRange.endDate,
        ...metrics
      }
    });
  }

  // Team Metrics Management
  async updateTeamMetrics(
    organizationId: string,
    period: MetricPeriod = 'daily'
  ): Promise<TeamMetrics> {
    const timeRange = this.getTimeRange(period);
    
    // Get all relevant data
    const [tasks, agents] = await Promise.all([
      prisma.task.findMany({
        where: {
          organizationId,
          createdAt: {
            gte: timeRange.startDate,
            lt: timeRange.endDate
          }
        },
        include: {
          metrics: true,
          escalations: true
        }
      }),
      prisma.user.findMany({
        where: {
          organizationId,
          role: 'AGENT'
        }
      })
    ]);

    // Calculate metrics
    const metrics = this.calculateTeamMetrics(tasks, agents);

    // Update or create team metrics
    return await prisma.teamMetrics.upsert({
      where: {
        organizationId_period_startDate: {
          organizationId,
          period,
          startDate: timeRange.startDate
        }
      },
      update: metrics,
      create: {
        organizationId,
        period,
        startDate: timeRange.startDate,
        endDate: timeRange.endDate,
        ...metrics
      }
    });
  }

  // Metric Calculations
  private calculateTaskMetrics(task: Task): Partial<TaskMetrics> {
    const {
      createdAt,
      assignedAt,
      firstResponseAt,
      resolvedAt,
      slaPolicy
    } = task;

    // Calculate durations
    const waitTime = assignedAt
      ? this.getMinutesDiff(createdAt, assignedAt)
      : null;
      
    const handleTime = resolvedAt && assignedAt
      ? this.getMinutesDiff(assignedAt, resolvedAt)
      : null;
      
    const totalTime = resolvedAt
      ? this.getMinutesDiff(createdAt, resolvedAt)
      : null;

    // Calculate SLA compliance
    const metResponseSLA = slaPolicy && firstResponseAt
      ? this.getMinutesDiff(createdAt, firstResponseAt) <= slaPolicy.responseTime
      : null;

    const metResolveSLA = slaPolicy && resolvedAt
      ? this.getMinutesDiff(createdAt, resolvedAt) <= slaPolicy.resolutionTime
      : null;

    return {
      waitTime,
      handleTime,
      totalTime,
      metResponseSLA,
      metResolveSLA
    };
  }

  private calculateAgentMetrics(tasks: Task[]): Partial<AgentMetrics> {
    const completedTasks = tasks.filter(t => isCompletedStatus(t.status));
    const escalatedTasks = tasks.filter(t => t.escalations.length > 0);

    // Calculate response and handle times
    const responseTimes = tasks
      .filter(t => t.firstResponseAt)
      .map(t => this.getMinutesDiff(t.createdAt, t.firstResponseAt!));

    const handleTimes = completedTasks
      .map(t => this.getMinutesDiff(t.assignedAt!, t.resolvedAt!));

    return {
      tasksAssigned: tasks.length,
      tasksCompleted: completedTasks.length,
      tasksEscalated: escalatedTasks.length,
      avgResponseTime: this.calculateAverage(responseTimes),
      avgHandleTime: this.calculateAverage(handleTimes),
      completionRate: tasks.length > 0
        ? completedTasks.length / tasks.length
        : 0,
      slaComplianceRate: this.calculateSLACompliance(tasks)
    };
  }

  private calculateTeamMetrics(
    tasks: Task[],
    agents: User[]
  ): Partial<TeamMetrics> {
    const completedTasks = tasks.filter(t => isCompletedStatus(t.status));
    const escalatedTasks = tasks.filter(t => t.escalations.length > 0);

    // Calculate team-wide metrics
    return {
      totalTasks: tasks.length,
      completedTasks: completedTasks.length,
      escalatedTasks: escalatedTasks.length,
      avgResponseTime: this.calculateAverageResponseTime(tasks),
      avgHandleTime: this.calculateAverageHandleTime(completedTasks),
      slaComplianceRate: this.calculateSLACompliance(tasks),
      activeAgents: agents.filter(a => isAvailableStatus(a.status)).length,
      utilizationRate: this.calculateUtilizationRate(tasks, agents)
    };
  }

  // Utility Methods
  private getTimeRange(period: MetricPeriod) {
    const now = new Date();
    let startDate: Date;
    let endDate: Date;

    switch (period) {
      case 'daily':
        startDate = new Date(now.setHours(0, 0, 0, 0));
        endDate = new Date(now.setHours(23, 59, 59, 999));
        break;
      case 'weekly':
        startDate = new Date(now.setDate(now.getDate() - now.getDay()));
        endDate = new Date(now.setDate(now.getDate() + 6));
        break;
      case 'monthly':
        startDate = new Date(now.setDate(1));
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
      default:
        throw new Error('Invalid period');
    }

    return { startDate, endDate };
  }

  private getMinutesDiff(start: Date, end: Date): number {
    return Math.round((end.getTime() - start.getTime()) / 1000 / 60);
  }

  private calculateAverage(numbers: number[]): number {
    return numbers.length > 0
      ? numbers.reduce((sum, n) => sum + n, 0) / numbers.length
      : 0;
  }

  private calculateSLACompliance(tasks: Task[]): number {
    const tasksWithSLA = tasks.filter(t => t.slaPolicy);
    if (tasksWithSLA.length === 0) return 0;

    const compliantTasks = tasksWithSLA.filter(t => {
      return isTaskWithMetrics(t) && t.metrics.metResponseSLA && t.metrics.metResolveSLA;
    });

    return compliantTasks.length / tasksWithSLA.length;
  }

  private calculateUtilizationRate(tasks: Task[], agents: User[]): number {
    if (agents.length === 0) return 0;

    const totalCapacity = agents.reduce((sum, agent) => 
      sum + (agent.maxConcurrentTasks || 5), 0);

    const avgActiveTasks = tasks.length / agents.length;

    return Math.min(1, avgActiveTasks / totalCapacity);
  }

  private calculateAverageResponseTime(tasks: Task[]): number {
    const responseTimes = tasks
      .filter(t => t.firstResponseAt)
      .map(t => this.getMinutesDiff(t.createdAt, t.firstResponseAt!));

    return this.calculateAverage(responseTimes);
  }

  private calculateAverageHandleTime(tasks: Task[]): number {
    const handleTimes = tasks
      .filter(t => t.resolvedAt && t.assignedAt)
      .map(t => this.getMinutesDiff(t.assignedAt!, t.resolvedAt!));

    return this.calculateAverage(handleTimes);
  }
}