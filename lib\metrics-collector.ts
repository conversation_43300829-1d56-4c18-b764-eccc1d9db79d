import { prisma } from './prisma'
import { 
  User, 
  Task,
  type TaskRating
} from '@prisma/client'

type TaskWithIncludes = Task & {
  rating?: TaskRating | null;
}

interface AgentMetricsData {
  agentId: string;
  organizationId: string;
  metricType: string;
  periodStart: Date;
  periodEnd: Date;
  tasksAssigned: number;
  tasksCompleted: number;
  tasksEscalated: number;
  avgResponseTime: number;
  avgResolutionTime: number;
  totalWorkTime: number;
  qualityScore: number;
  customerRating: number;
  slaResponseMet: number;
  slaResolutionMet: number;
  slaResponseTotal: number;
  slaResolutionTotal: number;
}

interface SystemMetricsData {
  organizationId: string;
  metricType: string;
  timestamp: Date;
  totalTasks: number;
  tasksRouted: number;
  routingFailures: number;
  avgRoutingTime: number;
  agentsActive: number;
  agentsIdle: number;
  avgUtilization: number;
  slaCompliance: number;
  escalationRate: number;
}

export class MetricsCollector {
  // Real-time update handlers
  async onTaskCompleted(taskId: string, agentId: string): Promise<void> {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: { rating: true }
    });

    if (!task) {
      throw new Error('Task not found');
    }

    // Update agent metrics immediately
    await this.collectAgentMetrics(agentId, 'daily');
    
    // Update system metrics for the organization
    await this.collectSystemMetrics(task.organizationId, 'hourly');
  }

  async onTaskAssigned(taskId: string, agentId: string): Promise<void> {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: { rating: true }
    });

    if (!task) {
      throw new Error('Task not found');
    }

    // Update routing metrics
    const metrics = await prisma.systemMetric.findFirst({
      where: {
        organizationId: task.organizationId,
        metricType: 'hourly'
      },
      orderBy: { timestamp: 'desc' }
    });

    if (metrics) {
      const routingTime = task.assignedAt ?
        (task.assignedAt.getTime() - task.createdAt.getTime()) / (1000 * 60) : 0;

      await prisma.systemMetric.update({
        where: { id: metrics.id },
        data: {
          tasksRouted: metrics.tasksRouted + 1,
          avgRoutingTime: this.calculateNewAverage(
            metrics.avgRoutingTime,
            routingTime,
            metrics.tasksRouted
          )
        }
      });
    }

    // Update agent metrics
    await this.collectAgentMetrics(agentId, 'daily');
  }

  private calculateNewAverage(currentAvg: number, newValue: number, currentCount: number): number {
    return (currentAvg * currentCount + newValue) / (currentCount + 1);
  }

  // Helper method to get date range for a period
  private getPeriodRange(period: 'daily' | 'weekly' | 'monthly' | 'hourly', date: Date = new Date()) {
    const start = new Date(date)
    const end = new Date(date)
    
    switch (period) {
      case 'hourly':
        start.setMinutes(0, 0, 0)
        end.setMinutes(59, 59, 999)
        break
      case 'daily':
        start.setHours(0, 0, 0, 0)
        end.setHours(23, 59, 59, 999)
        break
      case 'weekly':
        start.setDate(start.getDate() - start.getDay()) // Start of week (Sunday)
        end.setDate(end.getDate() - end.getDay() + 6) // End of week (Saturday)
        start.setHours(0, 0, 0, 0)
        end.setHours(23, 59, 59, 999)
        break
      case 'monthly':
        start.setDate(1) // Start of month
        end.setMonth(end.getMonth() + 1)
        end.setDate(0) // End of month
        start.setHours(0, 0, 0, 0)
        end.setHours(23, 59, 59, 999)
        break
    }
    
    return { start, end }
  }

  // Agent performance metrics collection
  async collectAgentMetrics(agentId: string, period: 'daily' | 'weekly' | 'monthly') {
    const { start, end } = this.getPeriodRange(period)
    
    // Get task data for the period
    const tasks = await prisma.task.findMany({
      where: {
        assignedTo: agentId,
        createdAt: { gte: start, lte: end }
      },
      include: {
        rating: true
      }
    })

    // Get agent details for organization ID
    const agent = await prisma.user.findUnique({
      where: { id: agentId },
      select: { organizationId: true }
    })

    if (!agent) {
      throw new Error('Agent not found')
    }

    const metrics = this.calculateAgentMetrics(tasks, agentId, agent.organizationId, period, start, end)
    
    // Store or update metrics
    return await this.storeAgentMetrics(metrics)
  }

  private calculateAgentMetrics(
    tasks: TaskWithIncludes[],
    agentId: string,
    organizationId: string,
    period: string,
    start: Date,
    end: Date
  ): AgentMetricsData {
    const completedTasks = tasks.filter(t => t.status === 'COMPLETED')
    const escalatedTasks = tasks.filter(t => t.status === 'ESCALATED')
    
    // Calculate response times
    const responseTimes = tasks
      .filter(t => t.respondedAt)
      .map(t => {
        const responseTime = t.respondedAt!.getTime() - t.createdAt.getTime()
        return responseTime / (1000 * 60) // Convert to minutes
      })
    
    // Calculate resolution times
    const resolutionTimes = completedTasks
      .filter(t => t.resolvedAt)
      .map(t => {
        const resolutionTime = t.resolvedAt!.getTime() - t.createdAt.getTime()
        return resolutionTime / (1000 * 60) // Convert to minutes
      })
    
    // Calculate quality metrics
    const ratings = tasks.filter(t => t.rating).map(t => t.rating!.rating)
    const avgRating = ratings.length > 0 ? 
      ratings.reduce((a, b) => a + b, 0) / ratings.length : 0
    
    // Calculate SLA compliance
    const slaResponseMet = tasks.filter(t => !t.breachedSLA && t.respondedAt).length
    const slaResolutionMet = tasks.filter(t => !t.breachedSLA && t.resolvedAt).length
    const tasksWithSLA = tasks.filter(t => t.resolveBy || t.responseBy).length

    return {
      agentId,
      organizationId,
      metricType: period,
      periodStart: start,
      periodEnd: end,
      
      tasksAssigned: tasks.length,
      tasksCompleted: completedTasks.length,
      tasksEscalated: escalatedTasks.length,
      
      avgResponseTime: responseTimes.length > 0 ? 
        responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0,
      avgResolutionTime: resolutionTimes.length > 0 ? 
        resolutionTimes.reduce((a, b) => a + b, 0) / resolutionTimes.length : 0,
      totalWorkTime: this.calculateTotalWorkTime(tasks),
      
      qualityScore: this.calculateQualityScore(tasks),
      customerRating: avgRating,
      
      slaResponseMet,
      slaResolutionMet,
      slaResponseTotal: tasksWithSLA,
      slaResolutionTotal: tasksWithSLA
    }
  }

  private calculateTotalWorkTime(tasks: TaskWithIncludes[]): number {
    return tasks.reduce((total, task) => {
      if (task.resolvedAt) {
        const workTime = task.resolvedAt.getTime() - task.createdAt.getTime()
        return total + (workTime / (1000 * 60)) // Convert to minutes
      }
      return total
    }, 0)
  }

  private calculateQualityScore(tasks: TaskWithIncludes[]): number {
    const ratedTasks = tasks.filter(t => t.rating)
    if (ratedTasks.length === 0) return 0
    
    const totalScore = ratedTasks.reduce((score, task) => {
      // Base score from customer rating (1-5 scale)
      let taskScore = task.rating!.rating
      
      // Adjust score based on SLA compliance
      if (task.responseBy || task.resolveBy) {
        if (task.respondedAt && task.respondedAt <= task.responseBy!) taskScore += 0.5
        if (task.resolvedAt && task.resolvedAt <= task.resolveBy!) taskScore += 0.5
      }
      
      // Penalize for escalations
      if (task.status === 'ESCALATED') taskScore -= 1
      
      return score + Math.max(1, Math.min(5, taskScore))
    }, 0)
    
    return totalScore / ratedTasks.length
  }

  private async storeAgentMetrics(metrics: AgentMetricsData) {
    return await prisma.performanceMetric.upsert({
      where: {
        agentId_metricType_periodStart: {
          agentId: metrics.agentId,
          metricType: metrics.metricType,
          periodStart: metrics.periodStart
        }
      },
      update: metrics,
      create: metrics
    })
  }

  // System metrics collection
  async collectSystemMetrics(organizationId: string, period: 'hourly' | 'daily' | 'weekly') {
    const { start, end } = this.getPeriodRange(period)
    
    // Get system-wide data
    const tasks = await prisma.task.findMany({
      where: {
        organizationId,
        createdAt: { gte: start, lte: end }
      }
    })
    
    const agents = await prisma.user.findMany({
      where: {
        organizationId,
        role: 'AGENT'
      }
    })
    
    const metrics = this.calculateSystemMetrics(tasks, agents, organizationId, period, end)
    
    return await this.storeSystemMetrics(metrics)
  }

  private calculateSystemMetrics(
    tasks: Task[],
    agents: User[],
    organizationId: string,
    period: string,
    timestamp: Date
  ): SystemMetricsData {
    const routedTasks = tasks.filter(t => t.assignedTo)
    const failedRouting = tasks.filter(t => !t.assignedTo && t.status === 'PENDING')
    
    const activeAgents = agents.filter(a => 
      a.status === 'AVAILABLE' || a.currentTaskCount > 0
    )
    const idleAgents = agents.filter(a => 
      a.status === 'AVAILABLE' && a.currentTaskCount === 0
    )
    
    // Calculate utilization
    const totalCapacity = agents.reduce((sum, a) => sum + a.maxConcurrentTasks, 0)
    const currentLoad = agents.reduce((sum, a) => sum + a.currentTaskCount, 0)
    const utilization = totalCapacity > 0 ? (currentLoad / totalCapacity) * 100 : 0
    
    // Calculate routing time
    const routingTimes = routedTasks
      .filter(t => t.assignedAt)
      .map(t => {
        const routingTime = t.assignedAt!.getTime() - t.createdAt.getTime()
        return routingTime / (1000 * 60) // Convert to minutes
      })
    
    // Calculate SLA compliance
    const slaCompliantTasks = tasks.filter(t => !t.breachedSLA)
    const slaCompliance = tasks.length > 0 ? 
      (slaCompliantTasks.length / tasks.length) * 100 : 0
    
    const escalatedTasks = tasks.filter(t => t.status === 'ESCALATED')
    const escalationRate = tasks.length > 0 ? 
      (escalatedTasks.length / tasks.length) * 100 : 0
    
    return {
      organizationId,
      metricType: period,
      timestamp,
      
      totalTasks: tasks.length,
      tasksRouted: routedTasks.length,
      routingFailures: failedRouting.length,
      avgRoutingTime: routingTimes.length > 0 ?
        routingTimes.reduce((a, b) => a + b, 0) / routingTimes.length : 0,
      
      agentsActive: activeAgents.length,
      agentsIdle: idleAgents.length,
      avgUtilization: utilization,
      
      slaCompliance,
      escalationRate
    }
  }

  private async storeSystemMetrics(metrics: SystemMetricsData) {
    return await prisma.systemMetric.upsert({
      where: {
        organizationId_metricType_timestamp: {
          organizationId: metrics.organizationId,
          metricType: metrics.metricType,
          timestamp: metrics.timestamp
        }
      },
      update: metrics,
      create: metrics
    })
  }
}