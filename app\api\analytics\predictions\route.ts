import { NextResponse } from 'next/server'
import { z } from 'zod'
import { AnalyticsEngine } from '@/lib/analytics-engine'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

const querySchema = z.object({
  organizationId: z.string(),
  period: z.string().optional().default('30d')
})

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Extract and validate query parameters
    const url = new URL(request.url)
    const queryParams = {
      organizationId: url.searchParams.get('organizationId'),
      period: url.searchParams.get('period')
    }

    const validatedParams = querySchema.safeParse(queryParams)
    if (!validatedParams.success) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: validatedParams.error.format() },
        { status: 400 }
      )
    }

    // Check if user has access to this organization
    if (session.user.organizationId !== validatedParams.data.organizationId) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    const analytics = new AnalyticsEngine()
    const predictions = await analytics.getPredictiveInsights(
      validatedParams.data.organizationId,
      validatedParams.data.period
    )

    return NextResponse.json(predictions)
  } catch (error) {
    console.error('Predictive analytics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch predictive analytics' },
      { status: 500 }
    )
  }
}