"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import {
  Users,
  Activity,
  TrendingUp,
  CheckCircle,
  RefreshCw,
  Search,
  Eye,
  Clock,
  UserCheck
} from "lucide-react"
import { toast } from "sonner"
import { TaskCreationForm } from "./task-creation-form"
import { AdminTaskManagementModal } from "./admin-task-management-modal"
import { WorkingHoursManager } from "./working-hours-manager"
import { AvailabilityManager } from "./availability-manager"
import { OrganizationSettings } from "./organization-settings"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { format } from "date-fns"

// Wrapper components for modal state management
interface WorkingHoursDialogProps {
  agent: {
    id: string
    name: string
  }
  onModalOpen: () => void
  onModalClose: () => void
}

function WorkingHoursDialog({ agent, onModalOpen, onModalClose }: WorkingHoursDialogProps) {
  const [open, setOpen] = useState(false)

  useEffect(() => {
    if (open) {
      onModalOpen()
    } else {
      onModalClose()
    }
  }, [open, onModalOpen, onModalClose])

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Clock className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Working Hours - {agent.name}</DialogTitle>
          <DialogDescription>
            Manage working hours and schedule for {agent.name}
          </DialogDescription>
        </DialogHeader>
        <div className="max-h-[70vh] overflow-y-auto">
          <WorkingHoursManager agentId={agent.id} />
        </div>
      </DialogContent>
    </Dialog>
  )
}

interface AvailabilityDialogProps {
  agent: {
    id: string
    name: string
  }
  currentStatus: string
  onStatusChange: () => void
  onModalOpen: () => void
  onModalClose: () => void
}

function AvailabilityDialog({ agent, currentStatus, onStatusChange, onModalOpen, onModalClose }: AvailabilityDialogProps) {
  const [open, setOpen] = useState(false)

  useEffect(() => {
    if (open) {
      onModalOpen()
    } else {
      onModalClose()
    }
  }, [open, onModalOpen, onModalClose])

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <UserCheck className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Availability - {agent.name}</DialogTitle>
          <DialogDescription>
            Manage availability status and temporary periods for {agent.name}
          </DialogDescription>
        </DialogHeader>
        <div className="max-h-[70vh] overflow-y-auto">
          <AvailabilityManager
            agentId={agent.id}
            currentStatus={currentStatus}
            onStatusChange={onStatusChange}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}

interface RoutingStats {
  routing: {
    totalAgents: number
    availableAgents: number
    totalTasks: number
    assignedTasks: number
  }
  agents: {
    total: number
    available: number
    busy: number
    away: number
    offline: number
    details: Array<{
      id: string
      name: string
      email: string
      status: string
      currentTasks: number
      maxTasks: number
      utilizationRate: number
      isAvailable: boolean
    }>
  }
  tasks: {
    distribution: {
      pending: number
      assigned: number
      in_progress: number
      completed: number
      escalated: number
    }
    total: number
  }
  capacity: {
    total: number
    used: number
    available: number
    utilizationRate: number
  }
  performance: {
    assignmentSuccessRate: number
    averageUtilization: number
  }
  recentActivity: Array<{
    id: string
    taskId: string
    taskTitle: string
    taskPriority: string
    agentName: string
    assignedAt: string
  }>
}

interface Task {
  id: string
  title: string
  description?: string
  priority: string
  type: string
  status: string
  estimatedDuration?: number
  source: string
  assignedTo?: string
  assignedAt?: string
  createdAt: string
  updatedAt: string
  assignedUser?: {
    id: string
    name: string
    email: string
  }
  requiredSkills?: Array<{
    skillName: string
    requiredLevel: number
  }>
}

export function AdminDashboard() {
  const [stats, setStats] = useState<RoutingStats | null>(null)
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [tasksLoading, setTasksLoading] = useState(false)
  const [organizationId, setOrganizationId] = useState<string>('')
  const [agentAvailability, setAgentAvailability] = useState<Record<string, string>>({})

  // Track modal states to prevent auto-refresh interference
  const openModalsRef = useRef<Set<string>>(new Set())
  const [autoRefreshPaused, setAutoRefreshPaused] = useState(false)

  // Task filtering state
  const [taskFilters, setTaskFilters] = useState({
    status: '',
    priority: '',
    assignedTo: '',
    search: ''
  })

  const fetchStats = useCallback(async () => {
    try {
      setRefreshing(true)
      const response = await fetch("/api/routing/stats")
      const result = await response.json()

      if (result.success) {
        setStats(result.data)
      } else {
        throw new Error(result.error?.message || "Failed to fetch stats")
      }
    } catch (error) {
      console.error("Error fetching stats:", error)
      toast.error("Failed to load dashboard data")
    } finally {
      setRefreshing(false)
      setLoading(false)
    }
  }, [])

  const fetchTasks = useCallback(async () => {
    try {
      setTasksLoading(true)

      // Build query parameters
      const params = new URLSearchParams()
      if (taskFilters.status) params.append('status', taskFilters.status)
      if (taskFilters.priority) params.append('priority', taskFilters.priority)
      if (taskFilters.assignedTo) params.append('assignedTo', taskFilters.assignedTo)
      params.append('limit', '50') // Get more tasks for admin view

      const response = await fetch(`/api/tasks?${params.toString()}`)
      const result = await response.json()

      if (result.success) {
        let filteredTasks = result.data.tasks

        // Apply search filter on client side
        if (taskFilters.search) {
          const searchLower = taskFilters.search.toLowerCase()
          filteredTasks = filteredTasks.filter((task: Task) =>
            task.title.toLowerCase().includes(searchLower) ||
            task.description?.toLowerCase().includes(searchLower) ||
            task.type.toLowerCase().includes(searchLower)
          )
        }

        setTasks(filteredTasks)
      } else {
        throw new Error(result.error?.message || "Failed to fetch tasks")
      }
    } catch (error) {
      console.error("Error fetching tasks:", error)
      toast.error("Failed to load tasks")
    } finally {
      setTasksLoading(false)
    }
  }, [taskFilters])

  // Fetch organization ID from session
  const fetchOrganizationId = useCallback(async () => {
    try {
      const response = await fetch('/api/auth/session')
      const session = await response.json()
      if (session?.user?.organizationId) {
        setOrganizationId(session.user.organizationId)
      }
    } catch (error) {
      console.error('Error fetching organization ID:', error)
    }
  }, [])

  // Fetch availability status for all agents
  const fetchAgentAvailability = useCallback(async () => {
    if (!stats?.agents?.details) return

    try {
      const availabilityPromises = stats.agents.details.map(async (agent) => {
        try {
          const response = await fetch(`/api/agents/${agent.id}/availability`)
          const result = await response.json()
          return {
            agentId: agent.id,
            status: result.success && result.data.availability
              ? result.data.availability.status
              : 'available'
          }
        } catch (error) {
          console.error(`Error fetching availability for agent ${agent.id}:`, error)
          return { agentId: agent.id, status: 'available' }
        }
      })

      const availabilityResults = await Promise.all(availabilityPromises)
      const availabilityMap = availabilityResults.reduce((acc, { agentId, status }) => {
        acc[agentId] = status
        return acc
      }, {} as Record<string, string>)

      setAgentAvailability(availabilityMap)
    } catch (error) {
      console.error('Error fetching agent availability:', error)
    }
  }, [stats?.agents?.details])

  // Helper functions for modal state management
  const handleModalOpen = useCallback((modalId: string) => {
    openModalsRef.current.add(modalId)
    setAutoRefreshPaused(true)
  }, [])

  const handleModalClose = useCallback((modalId: string) => {
    openModalsRef.current.delete(modalId)

    // Re-enable auto-refresh if no modals are open
    if (openModalsRef.current.size === 0) {
      setAutoRefreshPaused(false)
    }
  }, [])

  useEffect(() => {
    fetchStats()
    fetchTasks()
    fetchOrganizationId()

    // Auto-refresh every 30 seconds, but only when no modals are open
    const interval = setInterval(() => {
      if (openModalsRef.current.size === 0) {
        fetchStats()
        fetchTasks()
        fetchAgentAvailability()
      }
    }, 30000)
    return () => clearInterval(interval)
  }, [fetchStats, fetchTasks, fetchOrganizationId, fetchAgentAvailability])

  // Fetch agent availability when stats change
  useEffect(() => {
    if (stats?.agents?.details) {
      fetchAgentAvailability()
    }
  }, [stats?.agents?.details, fetchAgentAvailability])

  // Fetch tasks when filters change
  useEffect(() => {
    fetchTasks()
  }, [taskFilters, fetchTasks])

  const handleTaskCreated = () => {
    // Refresh stats and tasks when a new task is created
    fetchStats()
    fetchTasks()
  }

  const handleRefresh = () => {
    // Allow manual refresh even when modals are open
    fetchStats()
    fetchTasks()
    fetchAgentAvailability()
  }

  if (loading) {
    return <div className="flex items-center justify-center p-8">Loading dashboard...</div>
  }

  if (!stats) {
    return <div className="flex items-center justify-center p-8">Failed to load dashboard data</div>
  }

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case "AVAILABLE": return "bg-green-500"
      case "BUSY": return "bg-red-500"
      case "AWAY": return "bg-yellow-500"
      case "OFFLINE": return "bg-gray-500"
      default: return "bg-gray-500"
    }
  }

  // Get the display status (prioritize availability status over user status)
  const getAgentDisplayStatus = (agent: { id: string; status?: string }) => {
    const availabilityStatus = agentAvailability[agent.id]
    if (availabilityStatus) {
      return availabilityStatus.toUpperCase()
    }
    return agent.status || 'AVAILABLE'
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "URGENT": return "bg-red-500 text-white"
      case "HIGH": return "bg-orange-500 text-white"
      case "MEDIUM": return "bg-yellow-500 text-white"
      case "LOW": return "bg-green-500 text-white"
      default: return "bg-gray-500 text-white"
    }
  }

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case "PENDING": return "bg-yellow-500"
      case "ASSIGNED": return "bg-blue-500"
      case "IN_PROGRESS": return "bg-purple-500"
      case "COMPLETED": return "bg-green-500"
      case "ESCALATED": return "bg-red-500"
      default: return "bg-gray-500"
    }
  }

  const formatTaskStatus = (status: string) => {
    return status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor team performance and task routing
          </p>
        </div>
        <div className="flex items-center gap-2">
          {autoRefreshPaused && (
            <div className="flex items-center gap-1 text-sm text-amber-600 bg-amber-50 px-2 py-1 rounded">
              <span className="w-2 h-2 bg-amber-500 rounded-full"></span>
              Auto-refresh paused
            </div>
          )}
          <TaskCreationForm
            onTaskCreated={handleTaskCreated}
            onModalOpen={() => handleModalOpen('task-creation')}
            onModalClose={() => handleModalClose('task-creation')}
          />
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing || tasksLoading}
            title={autoRefreshPaused ? "Manual refresh (auto-refresh paused)" : "Refresh data"}
          >
            <RefreshCw className={`h-4 w-4 ${refreshing || tasksLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <Users className="h-4 w-4" />
              Total Agents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.agents.total}</div>
            <div className="text-sm text-muted-foreground">
              {stats.agents.available} available
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Active Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.tasks.total}</div>
            <div className="text-sm text-muted-foreground">
              {stats.tasks.distribution.pending} pending
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              System Utilization
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.capacity.utilizationRate.toFixed(1)}%
            </div>
            <Progress value={stats.capacity.utilizationRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Assignment Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.performance.assignmentSuccessRate.toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">
              Success rate
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="agents">Agents</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="settings">Organization Settings</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Agent Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Agent Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500" />
                      <span>Available</span>
                    </div>
                    <span className="font-medium">{stats.agents.available}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-500" />
                      <span>Busy</span>
                    </div>
                    <span className="font-medium">{stats.agents.busy}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-yellow-500" />
                      <span>Away</span>
                    </div>
                    <span className="font-medium">{stats.agents.away}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-gray-500" />
                      <span>Offline</span>
                    </div>
                    <span className="font-medium">{stats.agents.offline}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Task Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Task Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>Pending</span>
                    <span className="font-medium">{stats.tasks.distribution.pending}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Assigned</span>
                    <span className="font-medium">{stats.tasks.distribution.assigned}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>In Progress</span>
                    <span className="font-medium">{stats.tasks.distribution.in_progress}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Completed</span>
                    <span className="font-medium">{stats.tasks.distribution.completed}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Escalated</span>
                    <span className="font-medium">{stats.tasks.distribution.escalated}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Capacity Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Capacity Overview</CardTitle>
              <CardDescription>
                System-wide task capacity and utilization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.capacity.total}</div>
                  <div className="text-sm text-muted-foreground">Total Capacity</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.capacity.used}</div>
                  <div className="text-sm text-muted-foreground">Used</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.capacity.available}</div>
                  <div className="text-sm text-muted-foreground">Available</div>
                </div>
              </div>
              <Progress value={stats.capacity.utilizationRate} className="h-3" />
              <div className="text-center text-sm text-muted-foreground mt-2">
                {stats.capacity.utilizationRate.toFixed(1)}% utilized
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="agents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Agent Performance</CardTitle>
              <CardDescription>
                Individual agent status and workload
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Agent</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Current Tasks</TableHead>
                    <TableHead>Capacity</TableHead>
                    <TableHead>Utilization</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {stats.agents.details.map((agent) => (
                    <TableRow key={agent.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{agent.name}</div>
                          <div className="text-sm text-muted-foreground">{agent.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getStatusColor(getAgentDisplayStatus(agent))} text-white`}>
                          {getAgentDisplayStatus(agent)}
                        </Badge>
                      </TableCell>
                      <TableCell>{agent.currentTasks}</TableCell>
                      <TableCell>{agent.maxTasks}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress value={agent.utilizationRate} className="w-16 h-2" />
                          <span className="text-sm">{agent.utilizationRate.toFixed(1)}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {/* Working Hours Management */}
                          <WorkingHoursDialog
                            agent={agent}
                            onModalOpen={() => handleModalOpen(`working-hours-${agent.id}`)}
                            onModalClose={() => handleModalClose(`working-hours-${agent.id}`)}
                          />

                          {/* Availability Management */}
                          <AvailabilityDialog
                            agent={agent}
                            currentStatus={getAgentDisplayStatus(agent)}
                            onStatusChange={() => {
                              // Refresh stats and availability when availability changes
                              fetchStats();
                              fetchAgentAvailability();
                            }}
                            onModalOpen={() => handleModalOpen(`availability-${agent.id}`)}
                            onModalClose={() => handleModalClose(`availability-${agent.id}`)}
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          {/* Task Distribution Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Task Distribution</CardTitle>
              <CardDescription>
                Current task status breakdown
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
                {Object.entries(stats.tasks.distribution).map(([status, count]) => (
                  <div key={status} className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold">{count}</div>
                    <div className="text-sm text-muted-foreground capitalize">
                      {status.replace('_', ' ')}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Task List */}
          <Card>
            <CardHeader>
              <CardTitle>All Tasks</CardTitle>
              <CardDescription>
                Complete list of tasks with filtering options
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search tasks..."
                      value={taskFilters.search}
                      onChange={(e) => setTaskFilters(prev => ({ ...prev, search: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select
                  value={taskFilters.status || "all"}
                  onValueChange={(value) => setTaskFilters(prev => ({ ...prev, status: value === "all" ? "" : value }))}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="ASSIGNED">Assigned</SelectItem>
                    <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                    <SelectItem value="COMPLETED">Completed</SelectItem>
                    <SelectItem value="ESCALATED">Escalated</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={taskFilters.priority || "all"}
                  onValueChange={(value) => setTaskFilters(prev => ({ ...prev, priority: value === "all" ? "" : value }))}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priority</SelectItem>
                    <SelectItem value="LOW">Low</SelectItem>
                    <SelectItem value="MEDIUM">Medium</SelectItem>
                    <SelectItem value="HIGH">High</SelectItem>
                    <SelectItem value="URGENT">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Task Table */}
              {tasksLoading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading tasks...</span>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Task</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Assigned To</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Skills</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tasks.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                          No tasks found
                        </TableCell>
                      </TableRow>
                    ) : (
                      tasks.map((task) => (
                        <TableRow key={task.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{task.title}</div>
                              {task.description && (
                                <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                                  {task.description}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={`${getTaskStatusColor(task.status)} text-white`}>
                              {formatTaskStatus(task.status)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getPriorityColor(task.priority)}>
                              {task.priority}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {task.assignedUser ? (
                              <div>
                                <div className="font-medium">{task.assignedUser.name}</div>
                                <div className="text-sm text-muted-foreground">{task.assignedUser.email}</div>
                              </div>
                            ) : (
                              <span className="text-muted-foreground">Unassigned</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{task.type}</Badge>
                          </TableCell>
                          <TableCell>
                            {task.requiredSkills && task.requiredSkills.length > 0 ? (
                              <div className="flex flex-wrap gap-1">
                                {task.requiredSkills.slice(0, 2).map((skill, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    {skill.skillName} ({skill.requiredLevel})
                                  </Badge>
                                ))}
                                {task.requiredSkills.length > 2 && (
                                  <Badge variant="secondary" className="text-xs">
                                    +{task.requiredSkills.length - 2}
                                  </Badge>
                                )}
                              </div>
                            ) : (
                              <span className="text-muted-foreground text-sm">No skills</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {format(new Date(task.createdAt), 'MMM dd, yyyy')}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {format(new Date(task.createdAt), 'HH:mm')}
                            </div>
                          </TableCell>
                          <TableCell>
                            <AdminTaskManagementModal
                              task={task}
                              onTaskUpdated={handleRefresh}
                              onModalOpen={() => handleModalOpen(`task-${task.id}`)}
                              onModalClose={() => handleModalClose(`task-${task.id}`)}
                              trigger={
                                <Button variant="ghost" size="sm">
                                  <Eye className="h-4 w-4" />
                                </Button>
                              }
                            />
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          {organizationId ? (
            <OrganizationSettings organizationId={organizationId} />
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading organization settings...</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest task assignments and updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.recentActivity.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No recent activity
                  </div>
                ) : (
                  stats.recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{activity.taskTitle}</div>
                        <div className="text-sm text-muted-foreground">
                          Assigned to {activity.agentName}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getPriorityColor(activity.taskPriority)}>
                          {activity.taskPriority}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(activity.assignedAt).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
