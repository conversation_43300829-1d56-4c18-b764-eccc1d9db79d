import { prisma } from './prisma'

interface MetricsBase {
  periodStart: Date;
  periodEnd: Date;
  metricType: string;
}

interface PerformanceMetricsData extends MetricsBase {
  avgResponseTime: number;
  avgResolutionTime: number;
  totalWorkTime: number;
  qualityScore: number;
  customerRating: number;
  tasksAssigned: number;
  tasksCompleted: number;
  tasksEscalated: number;
  slaResponseMet: number;
  slaResponseTotal: number;
  slaResolutionMet: number;
  slaResolutionTotal: number;
}

interface SystemMetricsData extends MetricsBase {
  totalTasks: number;
  tasksRouted: number;
  routingFailures: number;
  avgRoutingTime: number;
  avgUtilization: number;
  slaCompliance: number;
  escalationRate: number;
  timestamp: Date;
  agentsActive: number;
  agentsIdle: number;
}

// Helper types for Analytics Engine
export type MetricPeriod = 'hourly' | 'daily' | 'weekly' | 'monthly';

export interface AgentPerformanceSummary {
  agentId: string;
  agentName: string;
  period: string;
  
  efficiency: {
    completionRate: number;
    avgResponseTime: number;
    avgResolutionTime: number;
    utilization: number;
  };
  
  quality: {
    qualityScore: number;
    customerRating: number;
    escalationRate: number;
  };
  
  sla: {
    responseCompliance: number;
    resolutionCompliance: number;
  };
  
  trends: {
    completionRateTrend: number;
    qualityTrend: number;
    efficiencyTrend: number;
  };
}

type MetricKeys = keyof Pick<PerformanceMetricsData, 'qualityScore' | 'avgResponseTime' | 'avgResolutionTime'>;

export class AnalyticsEngine {
  // Agent performance analysis
  async getAgentPerformanceSummary(agentId: string, period = '30d'): Promise<AgentPerformanceSummary> {
    const metrics = await this.getAgentMetrics(agentId, period)
    const previousMetrics = await this.getAgentMetrics(agentId, this.getPreviousPeriod(period))
    
    const agent = await prisma.user.findUnique({
      where: { id: agentId },
      select: { name: true }
    })

    if (!agent) {
      throw new Error('Agent not found')
    }

    if (!metrics) {
      return {
        agentId,
        agentName: agent.name,
        period,
        efficiency: { completionRate: 0, avgResponseTime: 0, avgResolutionTime: 0, utilization: 0 },
        quality: { qualityScore: 0, customerRating: 0, escalationRate: 0 },
        sla: { responseCompliance: 0, resolutionCompliance: 0 },
        trends: { completionRateTrend: 0, qualityTrend: 0, efficiencyTrend: 0 }
      }
    }

    return {
      agentId,
      agentName: agent.name,
      period,
      
      efficiency: {
        completionRate: this.calculateCompletionRate(metrics),
        avgResponseTime: metrics.avgResponseTime,
        avgResolutionTime: metrics.avgResolutionTime,
        utilization: this.calculateUtilization(metrics)
      },
      
      quality: {
        qualityScore: metrics.qualityScore,
        customerRating: metrics.customerRating,
        escalationRate: this.calculateEscalationRate(metrics)
      },
      
      sla: {
        responseCompliance: this.calculateSLACompliance(metrics.slaResponseMet, metrics.slaResponseTotal),
        resolutionCompliance: this.calculateSLACompliance(metrics.slaResolutionMet, metrics.slaResolutionTotal)
      },
      
      trends: {
        completionRateTrend: previousMetrics ? this.calculateCompletionRateTrend(metrics, previousMetrics) : 0,
        qualityTrend: this.calculateTrend(metrics, previousMetrics, 'qualityScore'),
        efficiencyTrend: this.calculateEfficiencyTrend(metrics, previousMetrics)
      }
    }
  }

  // Helper methods
  private async getAgentMetrics(agentId: string, period: string): Promise<PerformanceMetricsData | null> {
    const { start, end } = this.parsePeriod(period)
    
    const result = await prisma.performanceMetric.findFirst({
      where: {
        agentId,
        periodStart: { gte: start },
        periodEnd: { lte: end }
      },
      orderBy: { periodStart: 'desc' }
    })

    return result as PerformanceMetricsData | null
  }

  private async getSystemMetrics(organizationId: string, period: string): Promise<SystemMetricsData[]> {
    const { start, end } = this.parsePeriod(period)
    
    const results = await prisma.systemMetric.findMany({
      where: {
        organizationId,
        timestamp: { gte: start, lte: end }
      },
      orderBy: { timestamp: 'asc' }
    })

    return results.map(result => ({
      ...result,
      periodStart: start,
      periodEnd: end
    }))
  }

  private parsePeriod(period: string): { start: Date; end: Date } {
    const end = new Date()
    const start = new Date()
    const match = period.match(/^(\d+)([dw])$/)
    
    if (!match) {
      throw new Error('Invalid period format. Use format like "30d" or "4w"')
    }
    
    const [, value, unit] = match
    const days = unit === 'w' ? parseInt(value) * 7 : parseInt(value)
    start.setDate(start.getDate() - days)
    
    return { start, end }
  }

  private getPreviousPeriod(period: string): string {
    const match = period.match(/^(\d+)([dw])$/)
    if (!match) return period
    
    const [, value, unit] = match
    return `${parseInt(value) * 2}${unit}`
  }

  private calculateCompletionRate(metrics: PerformanceMetricsData): number {
    return metrics.tasksCompleted / (metrics.tasksAssigned || 1) * 100
  }

  private calculateCompletionRateTrend(current: PerformanceMetricsData, previous: PerformanceMetricsData | null): number {
    if (!previous) return 0
    
    const currentRate = this.calculateCompletionRate(current)
    const previousRate = this.calculateCompletionRate(previous)
    
    return previousRate > 0 ? ((currentRate - previousRate) / previousRate) * 100 : 0
  }

  private calculateUtilization(metrics: PerformanceMetricsData): number {
    // Assuming 8 hour workday and totalWorkTime in minutes
    const workdayMinutes = 8 * 60
    return Math.min(100, (metrics.totalWorkTime / workdayMinutes) * 100)
  }

  private calculateEscalationRate(metrics: PerformanceMetricsData): number {
    return (metrics.tasksEscalated / (metrics.tasksAssigned || 1)) * 100
  }

  private calculateSLACompliance(met: number, total: number): number {
    return total > 0 ? (met / total) * 100 : 0
  }

  private calculateTrend(current: PerformanceMetricsData | null, previous: PerformanceMetricsData | null, metric: MetricKeys): number {
    if (!previous || !current) return 0
    const currentValue = current[metric]
    const previousValue = previous[metric]
    return previousValue > 0 ? ((currentValue - previousValue) / previousValue) * 100 : 0
  }

  private calculateEfficiencyTrend(current: PerformanceMetricsData | null, previous: PerformanceMetricsData | null): number {
    if (!previous || !current) return 0
    
    const currentEfficiency = (
      this.calculateCompletionRate(current) +
      this.calculateUtilization(current)
    ) / 2
    
    const previousEfficiency = (
      this.calculateCompletionRate(previous) +
      this.calculateUtilization(previous)
    ) / 2
    
    return previousEfficiency > 0 ? 
      ((currentEfficiency - previousEfficiency) / previousEfficiency) * 100 : 0
  }

  private calculateTeamAverage(metrics: AgentPerformanceSummary[], path: string): number {
    const values = metrics.map(m => {
      const value = this.getNestedValue(m, path)
      return typeof value === 'number' ? value : 0
    })
    
    return values.length > 0 ? 
      values.reduce((sum, v) => sum + v, 0) / values.length : 0
  }

  private getNestedValue(obj: AgentPerformanceSummary, path: string): unknown {
    return path.split('.').reduce<unknown>((acc, part) => {
      if (acc && typeof acc === 'object') {
        return (acc as Record<string, unknown>)[part]
      }
      return undefined
    }, obj)
  }

  // Team performance analysis
  async getTeamPerformanceAnalysis(organizationId: string, period = '30d'): Promise<{
    overview: {
      totalAgents: number;
      activeAgents: number;
      avgUtilization: number;
    };
    performance: {
      taskCompletion: number;
      avgResponseTime: number;
      avgResolutionTime: number;
      slaCompliance: number;
      customerSatisfaction: number;
    };
    distribution: {
      topPerformers: Array<{agentId: string; score: number}>;
      improvementNeeded: Array<{agentId: string; score: number}>;
    };
  }> {
    // Get all agents in organization with metrics in the period
    const agents = await prisma.user.findMany({
      where: {
        organizationId,
        role: 'AGENT'
      }
    })

    // Get performance metrics for all agents
    const agentMetrics = await Promise.all(
      agents.map(agent => this.getAgentPerformanceSummary(agent.id, period))
    )

    // Calculate team overview
    const activeAgents = agents.filter(a => a.status !== 'OFFLINE').length
    const utilizations = agentMetrics.map(m => m.efficiency.utilization)
    const avgUtilization = utilizations.reduce((a, b) => a + b, 0) / utilizations.length

    // Calculate performance metrics
    const completionRates = agentMetrics.map(m => m.efficiency.completionRate)
    const responseTimes = agentMetrics.map(m => m.efficiency.avgResponseTime)
    const resolutionTimes = agentMetrics.map(m => m.efficiency.avgResolutionTime)
    const slaScores = agentMetrics.map(m =>
      (m.sla.responseCompliance + m.sla.resolutionCompliance) / 2
    )
    const qualityScores = agentMetrics.map(m => m.quality.qualityScore)

    // Calculate overall metrics
    const avgTaskCompletion = completionRates.reduce((a, b) => a + b, 0) / completionRates.length
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
    const avgResolutionTime = resolutionTimes.reduce((a, b) => a + b, 0) / resolutionTimes.length
    const avgSLACompliance = slaScores.reduce((a, b) => a + b, 0) / slaScores.length
    const avgCustomerSatisfaction = qualityScores.reduce((a, b) => a + b, 0) / qualityScores.length

    // Identify top performers and those needing improvement
    const performanceScores = agentMetrics.map((metrics, index) => ({
      agentId: agents[index].id,
      score: (
        metrics.efficiency.completionRate +
        metrics.quality.qualityScore +
        (metrics.sla.responseCompliance + metrics.sla.resolutionCompliance) / 2
      ) / 3
    }))

    performanceScores.sort((a, b) => b.score - a.score)
    
    return {
      overview: {
        totalAgents: agents.length,
        activeAgents,
        avgUtilization
      },
      performance: {
        taskCompletion: avgTaskCompletion,
        avgResponseTime,
        avgResolutionTime,
        slaCompliance: avgSLACompliance,
        customerSatisfaction: avgCustomerSatisfaction
      },
      distribution: {
        topPerformers: performanceScores.slice(0, 3),
        improvementNeeded: performanceScores.slice(-3).reverse()
      }
    }
  }

  // System performance analysis
  async getSystemPerformanceAnalysis(organizationId: string, period = '30d'): Promise<{
    routing: {
      efficiency: number;
      avgRoutingTime: number;
      successRate: number;
    };
    resources: {
      utilization: number;
      distribution: number;
      capacity: number;
    };
    health: {
      slaCompliance: number;
      escalationRate: number;
      systemLoad: number;
    };
  }> {
    const metrics = await this.getSystemMetrics(organizationId, period)
    
    if (!metrics.length) {
      return {
        routing: { efficiency: 0, avgRoutingTime: 0, successRate: 0 },
        resources: { utilization: 0, distribution: 0, capacity: 0 },
        health: { slaCompliance: 0, escalationRate: 0, systemLoad: 0 }
      }
    }

    // Calculate routing metrics
    const totalRoutingAttempts = metrics.reduce((sum, m) => sum + m.tasksRouted + m.routingFailures, 0)
    const successfulRoutings = metrics.reduce((sum, m) => sum + m.tasksRouted, 0)
    const avgRoutingTime = metrics.reduce((sum, m) => sum + m.avgRoutingTime, 0) / metrics.length
    const routingEfficiency = (successfulRoutings / (totalRoutingAttempts || 1)) * 100

    // Calculate resource metrics
    const avgUtilization = metrics.reduce((sum, m) => sum + m.avgUtilization, 0) / metrics.length
    const activeAgentsOverTime = metrics.map(m => m.agentsActive + m.agentsIdle || 0)
    const avgActiveAgents = activeAgentsOverTime.reduce((a, b) => a + b, 0) / activeAgentsOverTime.length
    const maxActiveAgents = Math.max(...activeAgentsOverTime)
    const resourceDistribution = (avgActiveAgents / (maxActiveAgents || 1)) * 100
    const resourceCapacity = 100 - avgUtilization

    // Calculate health metrics
    const slaCompliance = metrics.reduce((sum, m) => sum + m.slaCompliance, 0) / metrics.length
    const escalationRate = metrics.reduce((sum, m) => sum + m.escalationRate, 0) / metrics.length
    const systemLoad = (avgUtilization + (escalationRate / 2)) / 1.5 // Weighted metric

    return {
      routing: {
        efficiency: routingEfficiency,
        avgRoutingTime,
        successRate: (successfulRoutings / (totalRoutingAttempts || 1)) * 100
      },
      resources: {
        utilization: avgUtilization,
        distribution: resourceDistribution,
        capacity: resourceCapacity
      },
      health: {
        slaCompliance,
        escalationRate,
        systemLoad
      }
    }
  }

  // Predictive analytics
  async getPredictiveInsights(organizationId: string, period = '30d'): Promise<{
    workload: {
      predictedTasks: number;
      predictedPeakTime: string;
      recommendedAgents: number;
    };
    performance: {
      predictedSLA: number;
      potentialBottlenecks: string[];
      improvementAreas: Array<{area: string; impact: number}>;
    };
    recommendations: Array<{
      type: string;
      description: string;
      priority: 'high' | 'medium' | 'low';
      impact: number;
    }>;
  }> {
    const systemMetrics = await this.getSystemMetrics(organizationId, period)
    
    if (systemMetrics.length === 0) {
      return {
        workload: {
          predictedTasks: 0,
          predictedPeakTime: '00:00',
          recommendedAgents: 0
        },
        performance: {
          predictedSLA: 0,
          potentialBottlenecks: [],
          improvementAreas: []
        },
        recommendations: []
      }
    }

    // Analyze historical task patterns
    const taskCounts = systemMetrics.map(m => m.totalTasks)
    const avgTasksPerPeriod = taskCounts.reduce((a, b) => a + b, 0) / taskCounts.length
    const taskGrowthRate = this.calculateGrowthRate(taskCounts)
    
    // Predict future workload
    const predictedTasks = Math.round(avgTasksPerPeriod * (1 + taskGrowthRate))
    const peakMetric = systemMetrics.reduce((peak, metric) =>
      metric.totalTasks > peak.totalTasks ? metric : peak
    )
    const predictedPeakTime = this.formatPeakTime(peakMetric.timestamp)
    
    // Calculate resource needs
    const currentUtilization = systemMetrics[systemMetrics.length - 1]?.avgUtilization || 0
    const targetUtilization = 0.8 // 80% target utilization
    const recommendedAgents = Math.ceil(
      (predictedTasks / avgTasksPerPeriod) *
      ((systemMetrics[systemMetrics.length - 1]?.agentsActive || 0) +
       (systemMetrics[systemMetrics.length - 1]?.agentsIdle || 0) || 1) *
      (currentUtilization / targetUtilization)
    )

    // Analyze performance patterns
    const slaCompliance = systemMetrics.map(m => m.slaCompliance)
    const predictedSLA = this.calculateTrendPrediction(slaCompliance)
    
    // Identify potential issues
    const bottlenecks = this.identifyBottlenecks(systemMetrics)
    const improvementAreas = this.analyzeImprovementAreas(systemMetrics)
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(
      systemMetrics,
      { predictedTasks, currentUtilization, predictedSLA }
    )

    return {
      workload: {
        predictedTasks,
        predictedPeakTime,
        recommendedAgents
      },
      performance: {
        predictedSLA,
        potentialBottlenecks: bottlenecks,
        improvementAreas
      },
      recommendations
    }
  }

  private calculateGrowthRate(values: number[]): number {
    if (values.length < 2) return 0
    const oldValue = values[0]
    const newValue = values[values.length - 1]
    return oldValue ? (newValue - oldValue) / oldValue : 0
  }

  private formatPeakTime(date: Date): string {
    return `${date.getHours().toString().padStart(2, '0')}:00`
  }

  private calculateTrendPrediction(values: number[]): number {
    if (values.length < 2) return values[0] || 0
    const trend = this.calculateGrowthRate(values)
    return Math.min(100, Math.max(0, values[values.length - 1] * (1 + trend)))
  }

  private identifyBottlenecks(metrics: SystemMetricsData[]): string[] {
    const bottlenecks: string[] = []
    const latestMetric = metrics[metrics.length - 1]

    if (!latestMetric) return bottlenecks

    if (latestMetric.avgUtilization > 85) {
      bottlenecks.push('High agent utilization')
    }
    if (latestMetric.routingFailures / (latestMetric.tasksRouted || 1) > 0.1) {
      bottlenecks.push('Task routing efficiency')
    }
    if (latestMetric.escalationRate > 15) {
      bottlenecks.push('High escalation rate')
    }
    if (latestMetric.slaCompliance < 90) {
      bottlenecks.push('SLA compliance')
    }

    return bottlenecks
  }

  private analyzeImprovementAreas(metrics: SystemMetricsData[]): Array<{area: string; impact: number}> {
    const latestMetric = metrics[metrics.length - 1]
    if (!latestMetric) return []

    return [
      {
        area: 'Routing Optimization',
        impact: 100 - (latestMetric.tasksRouted / (latestMetric.totalTasks || 1) * 100)
      },
      {
        area: 'Resource Utilization',
        impact: Math.abs(80 - latestMetric.avgUtilization) // Deviation from optimal 80%
      },
      {
        area: 'SLA Management',
        impact: 100 - latestMetric.slaCompliance
      }
    ].sort((a, b) => b.impact - a.impact)
  }

  private generateRecommendations(
    metrics: SystemMetricsData[],
    predictions: { predictedTasks: number; currentUtilization: number; predictedSLA: number }
  ): Array<{type: string; description: string; priority: 'high' | 'medium' | 'low'; impact: number}> {
    const recommendations: Array<{type: string; description: string; priority: 'high' | 'medium' | 'low'; impact: number}> = []
    const latestMetric = metrics[metrics.length - 1]

    if (!latestMetric) return recommendations

    // Capacity recommendations
    if (predictions.currentUtilization > 80) {
      recommendations.push({
        type: 'capacity',
        description: 'Increase agent capacity to handle predicted workload increase',
        priority: 'high',
        impact: 90
      })
    }

    // Routing recommendations
    if (latestMetric.routingFailures / (latestMetric.tasksRouted || 1) > 0.05) {
      recommendations.push({
        type: 'routing',
        description: 'Optimize task routing rules to improve assignment success rate',
        priority: 'medium',
        impact: 75
      })
    }

    // SLA recommendations
    if (predictions.predictedSLA < 95) {
      recommendations.push({
        type: 'sla',
        description: 'Review and adjust SLA policies to improve compliance rates',
        priority: latestMetric.slaCompliance < 90 ? 'high' : 'medium',
        impact: 85
      })
    }

    return recommendations.sort((a, b) => b.impact - a.impact)
  }
}