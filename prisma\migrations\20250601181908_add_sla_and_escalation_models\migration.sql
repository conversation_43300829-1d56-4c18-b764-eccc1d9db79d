/*
  Warnings:

  - You are about to drop the column `responseDeadline` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `slaDeadline` on the `tasks` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "task_events" ADD COLUMN     "escalation_id" TEXT;

-- AlterTable
ALTER TABLE "tasks" DROP COLUMN "responseDeadline",
DROP COLUMN "slaDeadline",
ADD COLUMN     "breached_sla" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "resolve_by" TIMESTAMP(3),
ADD COLUMN     "resolved_at" TIMESTAMP(3),
ADD COLUMN     "responded_at" TIMESTAMP(3),
ADD COLUMN     "response_by" TIMESTAMP(3),
ADD COLUMN     "sla_policy_id" TEXT;

-- CreateTable
CREATE TABLE "sla_policies" (
    "id" TEXT NOT NULL,
    "organization_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "priority" TEXT NOT NULL,
    "response_time" INTEGER NOT NULL,
    "resolution_time" INTEGER NOT NULL,
    "escalationRules" JSONB NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sla_policies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "escalations" (
    "id" TEXT NOT NULL,
    "task_id" TEXT NOT NULL,
    "level" INTEGER NOT NULL,
    "reason" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "escalated_to" TEXT,
    "escalated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "resolved_at" TIMESTAMP(3),
    "comment" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "escalations_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "sla_policies_organization_id_name_key" ON "sla_policies"("organization_id", "name");

-- CreateIndex
CREATE INDEX "escalations_task_id_status_idx" ON "escalations"("task_id", "status");

-- CreateIndex
CREATE INDEX "tasks_sla_policy_id_idx" ON "tasks"("sla_policy_id");

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_sla_policy_id_fkey" FOREIGN KEY ("sla_policy_id") REFERENCES "sla_policies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sla_policies" ADD CONSTRAINT "sla_policies_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "escalations" ADD CONSTRAINT "escalations_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;
