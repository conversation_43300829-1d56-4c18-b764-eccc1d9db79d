import { NextResponse } from 'next/server'
import { z } from 'zod'
import { AnalyticsEngine } from '@/lib/analytics-engine'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

const querySchema = z.object({
  organizationId: z.string(),
  period: z.string().optional().default('30d'),
  metricType: z.enum(['routing', 'resources', 'health']).optional()
})

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Extract and validate query parameters
    const url = new URL(request.url)
    const queryParams = {
      organizationId: url.searchParams.get('organizationId'),
      period: url.searchParams.get('period'),
      metricType: url.searchParams.get('metricType')
    }

    const validatedParams = querySchema.safeParse(queryParams)
    if (!validatedParams.success) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: validatedParams.error.format() },
        { status: 400 }
      )
    }

    // Check if user has access to this organization
    if (session.user.organizationId !== validatedParams.data.organizationId) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    const analytics = new AnalyticsEngine()
    const systemMetrics = await analytics.getSystemPerformanceAnalysis(
      validatedParams.data.organizationId,
      validatedParams.data.period
    )

    // Filter metrics by type if specified
    if (validatedParams.data.metricType) {
      return NextResponse.json({
        [validatedParams.data.metricType]: systemMetrics[validatedParams.data.metricType]
      })
    }

    return NextResponse.json(systemMetrics)
  } catch (error) {
    console.error('System metrics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch system metrics' },
      { status: 500 }
    )
  }
}