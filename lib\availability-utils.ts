import { prisma } from './prisma';
import type { 
  WorkingHours, 
  AgentAvailability, 
  WorkHoursCreateInput,
  AvailabilityUpdateInput
} from './types';
import { UserStatus } from '@prisma/client';

export class AvailabilityManager {
  // Working Hours Management
  async setWorkingHours(agentId: string, schedule: WorkHoursCreateInput[]) {
    // Validate schedule
    this.validateSchedule(schedule);
    
    // Use transaction to update all working hours
    return await prisma.$transaction(async (tx) => {
      // Delete existing schedule
      await tx.workingHours.deleteMany({
        where: { agentId }
      });
      
      // Create new schedule entries
      const workingHours = await Promise.all(
        schedule.map(async (entry) => {
          return tx.workingHours.create({
            data: {
              agentId,
              dayOfWeek: entry.dayOfWeek,
              startTime: entry.startTime,
              endTime: entry.endTime,
              timeZone: entry.timeZone,
              isWorkDay: entry.isWorkDay ?? true
            }
          });
        })
      );
      
      return workingHours;
    });
  }

  async getWorkingHours(agentId: string): Promise<WorkingHours[]> {
    return await prisma.workingHours.findMany({
      where: { agentId },
      orderBy: { dayOfWeek: 'asc' }
    });
  }

  // Availability Management
  async updateAvailability(
    agentId: string, 
    status: UserStatus, 
    options?: AvailabilityUpdateInput
  ): Promise<AgentAvailability> {
    // Close any current availability period
    await this.closeCurrentAvailability(agentId);
    
    // Create new availability record
    return await prisma.agentAvailability.create({
      data: {
        agentId,
        status,
        statusMessage: options?.statusMessage,
        startTime: options?.startTime || new Date(),
        endTime: options?.endTime,
        isScheduled: options?.isScheduled || false
      }
    });
  }

  async getCurrentAvailability(agentId: string): Promise<AgentAvailability | null> {
    return await prisma.agentAvailability.findFirst({
      where: {
        agentId,
        endTime: null
      },
      orderBy: {
        startTime: 'desc'
      }
    });
  }

  async scheduleAvailability(
    agentId: string,
    status: UserStatus,
    startTime: Date,
    endTime: Date,
    statusMessage?: string
  ): Promise<AgentAvailability> {
    // Validate time range
    if (startTime >= endTime) {
      throw new Error('End time must be after start time');
    }

    // Check for schedule conflicts
    const conflicts = await prisma.agentAvailability.findMany({
      where: {
        agentId,
        isScheduled: true,
        OR: [
          {
            startTime: {
              lte: endTime
            },
            endTime: {
              gte: startTime
            }
          }
        ]
      }
    });

    if (conflicts.length > 0) {
      throw new Error('Schedule conflict detected');
    }

    return await prisma.agentAvailability.create({
      data: {
        agentId,
        status,
        statusMessage,
        startTime,
        endTime,
        isScheduled: true
      }
    });
  }

  // Utility Methods
  private async closeCurrentAvailability(agentId: string): Promise<void> {
    await prisma.agentAvailability.updateMany({
      where: {
        agentId,
        endTime: null
      },
      data: {
        endTime: new Date()
      }
    });
  }

  private validateSchedule(schedule: WorkHoursCreateInput[]): void {
    // Check for valid days
    const days = new Set(schedule.map(s => s.dayOfWeek));
    if (days.size !== schedule.length) {
      throw new Error('Duplicate days in schedule');
    }

    for (const entry of schedule) {
      // Validate day of week
      if (entry.dayOfWeek < 0 || entry.dayOfWeek > 6) {
        throw new Error('Invalid day of week');
      }

      // Validate time format (HH:mm)
      const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
      if (!timeRegex.test(entry.startTime) || !timeRegex.test(entry.endTime)) {
        throw new Error('Invalid time format');
      }

      // Validate time range
      const [startHour, startMinute] = entry.startTime.split(':').map(Number);
      const [endHour, endMinute] = entry.endTime.split(':').map(Number);
      const startMinutes = startHour * 60 + startMinute;
      const endMinutes = endHour * 60 + endMinute;

      if (endMinutes <= startMinutes) {
        throw new Error('End time must be after start time');
      }
    }
  }

  async isAgentAvailable(agentId: string, checkTime: Date = new Date()): Promise<boolean> {
    // Get agent's working hours for the day
    const dayOfWeek = checkTime.getDay();
    const workingHours = await prisma.workingHours.findFirst({
      where: {
        agentId,
        dayOfWeek
      }
    });

    // Check if it's a working day
    if (!workingHours?.isWorkDay) {
      return false;
    }

    // Check if time is within working hours
    const timeString = checkTime.toLocaleTimeString('en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    });

    if (timeString < workingHours.startTime || timeString > workingHours.endTime) {
      return false;
    }

    // Get current availability status
    const currentAvailability = await this.getCurrentAvailability(agentId);
    return currentAvailability?.status === UserStatus.AVAILABLE;
  }

  async getAvailableAgents(organizationId: string): Promise<string[]> {
    const agents = await prisma.user.findMany({
      where: {
        organizationId,
        role: 'AGENT'
      },
      select: {
        id: true
      }
    });

    const availableAgents = await Promise.all(
      agents.map(async (agent) => {
        const isAvailable = await this.isAgentAvailable(agent.id);
        return isAvailable ? agent.id : null;
      })
    );

    return availableAgents.filter((id): id is string => id !== null);
  }
}