import { prisma } from "@/lib/prisma";
import { getDashboardStats, getUserWorkloads } from "@/lib/db-utils";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TaskStatus, TaskPriority, UserStatus } from "@/lib/types";
import { Task } from "@prisma/client";

async function getDemoOrgId() {
  const org = await prisma.organization.findFirst({
    where: { name: "Demo Organization" },
  });
  return org?.id || "demo-org";
}

type TaskWithAssignedUser = Task & {
  assignedUser: { name: string; email: string } | null;
};

type UserWithWorkload = {
  id: string;
  name: string;
  email: string;
  status: UserStatus;
  currentTaskCount: number;
  maxConcurrentTasks: number;
};

export default async function Home() {
  const orgId = await getDemoOrgId();
  const [stats, , recentTasks, users] = await Promise.all([
    getDashboardStats(orgId),
    getUserWorkloads(orgId),
    prisma.task.findMany({
      where: { organizationId: orgId },
      include: {
        assignedUser: {
          select: { name: true, email: true },
        },
      },
      orderBy: { createdAt: "desc" },
      take: 5,
    }) as Promise<TaskWithAssignedUser[]>,
    prisma.user.findMany({
      where: { organizationId: orgId },
      select: {
        id: true,
        name: true,
        email: true,
        status: true,
        currentTaskCount: true,
        maxConcurrentTasks: true,
      },
    }),
  ]);

  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case "URGENT":
        return "bg-red-500";
      case "HIGH":
        return "bg-orange-500";
      case "MEDIUM":
        return "bg-yellow-500";
      case "LOW":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case "PENDING":
        return "bg-gray-500";
      case "ASSIGNED":
        return "bg-blue-500";
      case "IN_PROGRESS":
        return "bg-purple-500";
      case "COMPLETED":
        return "bg-green-500";
      case "ESCALATED":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getUserStatusColor = (status: UserStatus) => {
    switch (status) {
      case "AVAILABLE":
        return "bg-green-500";
      case "BUSY":
        return "bg-red-500";
      case "AWAY":
        return "bg-yellow-500";
      case "OFFLINE":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Task Routing Platform - Phase 1 MVP
          </h1>
          <p className="text-gray-600">
            Database schema successfully implemented with demo data
          </p>
          <div className="mt-4 space-x-4">
            <a
              href="/auth/signin"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
            >
              Sign In to Dashboard
            </a>
            <a
              href="/dashboard"
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 transition-colors"
            >
              View Demo Dashboard
            </a>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Total Tasks
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTasks}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Pending Tasks
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {stats.pendingTasks}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Completed Tasks
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats.completedTasks}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Available Agents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats.availableUsers}/{stats.totalUsers}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Tasks */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Tasks</CardTitle>
              <CardDescription>Latest tasks in the system</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTasks.map((task: TaskWithAssignedUser) => (
                  <div
                    key={task.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{task.title}</h4>
                      <p className="text-xs text-gray-500 mt-1">
                        {task.assignedUser
                          ? `Assigned to ${task.assignedUser.name}`
                          : "Unassigned"}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Badge
                        className={`${getPriorityColor(
                          task.priority
                        )} text-white text-xs`}
                      >
                        {task.priority}
                      </Badge>
                      <Badge
                        className={`${getStatusColor(
                          task.status
                        )} text-white text-xs`}
                      >
                        {task.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Team Members */}
          <Card>
            <CardHeader>
              <CardTitle>Team Members</CardTitle>
              <CardDescription>
                Current team status and workload
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {users.map((user: UserWithWorkload) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{user.name}</h4>
                      <p className="text-xs text-gray-500">{user.email}</p>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-xs text-gray-600">
                        {user.currentTaskCount}/{user.maxConcurrentTasks} tasks
                      </div>
                      <Badge
                        className={`${getUserStatusColor(
                          user.status
                        )} text-white text-xs`}
                      >
                        {user.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Database Test Link */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-medium text-blue-900 mb-2">
            Database Connection Test
          </h3>
          <p className="text-sm text-blue-700 mb-3">
            Test the database connection and view raw data
          </p>
          <a
            href="/api/test-db"
            target="_blank"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
          >
            Test Database API
          </a>
        </div>
      </div>
    </div>
  );
}
