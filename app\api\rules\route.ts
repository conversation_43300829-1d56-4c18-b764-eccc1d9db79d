import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get organizationId from query params
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    // Get rules for organization
    const rules = await prisma.routingRule.findMany({
      where: { organizationId },
      orderBy: { priority: 'desc' }
    });

    return NextResponse.json({ rules });
  } catch (error) {
    console.error('Error in GET /api/rules:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const {
      organizationId,
      name,
      description,
      priority,
      conditions,
      actions
    } = body;

    // Validate required fields
    if (!organizationId || !name || !conditions || !actions) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate conditions and actions format
    if (!Array.isArray(conditions) || !Array.isArray(actions)) {
      return NextResponse.json(
        { error: 'Conditions and actions must be arrays' },
        { status: 400 }
      );
    }

    // Create rule
    const rule = await prisma.routingRule.create({
      data: {
        organizationId,
        name,
        description,
        priority: priority || 0,
        conditions,
        actions,
        isActive: true
      }
    });

    return NextResponse.json(
      { rule },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/rules:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}