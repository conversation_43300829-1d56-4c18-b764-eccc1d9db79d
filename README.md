# Task Routing & Load Balancing Platform

A modern SaaS platform that intelligently distributes tasks (support tickets, leads, work requests) across team members based on availability, skills, workload, and business rules. Built with Next.js 14, TypeScript, and PostgreSQL.

## 🎯 Overview

This platform eliminates manual task assignment overhead by automatically routing tasks to the most suitable team members. It ensures optimal resource utilization while maintaining quality and response times, helping teams scale operations without proportional management overhead.

### Key Benefits

- **Intelligent Task Distribution** - Automatically assigns tasks based on agent availability, skills, and workload
- **Real-time Workload Balancing** - Prevents burnout by distributing tasks evenly across team members
- **Performance Tracking** - Monitor response times, completion rates, and team efficiency
- **Multi-tenant Architecture** - Supports multiple organizations with complete data isolation
- **Role-based Access Control** - Separate dashboards for admins and agents

## 🚀 Features

### ✅ Phase 1 (Complete)
- **User Authentication** - NextAuth.js with credential-based login
- **Task Management** - Create, assign, update, and track tasks
- **Agent Management** - Team member profiles with capacity tracking
- **Intelligent Routing** - Multiple assignment strategies (round-robin, least-loaded, weighted)
- **Real-time Dashboards** - Admin and agent-specific views
- **API Endpoints** - Complete REST API for all operations
- **Database Schema** - Optimized PostgreSQL schema with audit trails

### 🔄 Phase 2 (Planned)
- **Skill-based Routing** - Match tasks to agents based on required skills
- **SLA Management** - Deadline tracking and escalation workflows
- **Advanced Analytics** - Performance metrics and reporting
- **Working Hours** - Timezone-aware availability management

### 🎯 Phase 3 (Future)
- **Custom Routing Rules** - Business logic engine for complex assignment rules
- **Multi-channel Integration** - Email, API, and form-based task ingestion
- **Real-time Notifications** - WebSocket-based live updates
- **Advanced Reporting** - Comprehensive analytics and exports

## 🛠️ Technology Stack

- **Frontend**: Next.js 14 with TypeScript
- **Backend**: Next.js API Routes
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **UI Components**: shadcn/ui with Tailwind CSS
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts for analytics
- **Deployment**: Vercel-ready

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd task-saas
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Configure the following variables:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/taskrouting"
   NEXTAUTH_SECRET="your-secret-key"
   NEXTAUTH_URL="http://localhost:3000"
   ```

4. **Set up the database**
   ```bash
   # Generate Prisma client
   npm run db:generate

   # Push schema to database
   npm run db:push

   # Seed with demo data
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎮 Demo Accounts

The application comes with pre-seeded demo accounts:

### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Access**: Full system administration

### Agent Accounts
- **Alice Johnson**: <EMAIL> / agent123
- **Bob Smith**: <EMAIL> / agent123
- **Charlie Brown**: <EMAIL> / agent123

## 📊 API Documentation

### Task Management
```
GET    /api/tasks              # List tasks with filtering
POST   /api/tasks              # Create new task
GET    /api/tasks/[id]          # Get task details
PATCH  /api/tasks/[id]          # Update task
DELETE /api/tasks/[id]          # Delete task
POST   /api/tasks/[id]/assign   # Manual task assignment
```

### Agent Management
```
GET    /api/agents             # List agents
POST   /api/agents             # Create agent
GET    /api/agents/[id]         # Get agent details
PATCH  /api/agents/[id]         # Update agent
DELETE /api/agents/[id]         # Delete agent
PATCH  /api/agents/[id]/status  # Update availability
```

### Analytics
```
GET    /api/routing/stats       # Routing statistics and metrics
GET    /api/test-db             # Database connection test
```

## 🗄️ Database Schema

### Core Models
- **Organization** - Multi-tenant isolation
- **User** - Agents with capacity and status tracking
- **Task** - Core task entity with assignment tracking
- **TaskEvent** - Complete audit trail

### Key Features
- Multi-tenant architecture with organization-based isolation
- User capacity management (current vs max concurrent tasks)
- Task priority and status tracking with SLA support
- Comprehensive audit trail via task events
- Optimized indexes for performance

## 🎯 Routing Algorithms

### Round Robin
Fair distribution based on last assignment time

### Least Loaded
Assigns to agent with lowest current utilization

### Weighted Round Robin
Combines workload, performance, and skill factors for optimal assignment

## 📱 User Interface

### Admin Dashboard
- System-wide metrics and statistics
- Team management and oversight
- Task creation and assignment
- Performance monitoring

### Agent Dashboard
- Personal task queue
- Status management
- Task updates and completion
- Individual performance metrics

## 🔧 Development Scripts

```bash
# Development
npm run dev              # Start development server
npm run build            # Build for production
npm run start            # Start production server
npm run lint             # Run ESLint

# Database
npm run db:generate      # Generate Prisma client
npm run db:push          # Push schema to database
npm run db:migrate       # Run database migrations
npm run db:studio        # Open Prisma Studio
npm run db:seed          # Seed demo data
```

## 🏗️ Project Structure

```
├── app/                     # Next.js App Router
│   ├── admin/              # Admin dashboard
│   ├── agent/              # Agent dashboard
│   ├── auth/               # Authentication pages
│   ├── dashboard/          # Main dashboard
│   ├── api/                # API routes
│   │   ├── agents/         # Agent management endpoints
│   │   ├── auth/           # Authentication endpoints
│   │   ├── routing/        # Routing statistics
│   │   └── tasks/          # Task management endpoints
│   ├── layout.tsx          # Root layout
│   └── page.tsx            # Homepage
├── components/             # React components
│   ├── ui/                 # shadcn/ui components
│   ├── admin-dashboard.tsx # Admin interface
│   ├── agent-dashboard.tsx # Agent interface
│   ├── task-creation-form.tsx # Task creation modal
│   └── providers.tsx       # Context providers
├── lib/                    # Utility libraries
│   ├── auth.ts             # NextAuth configuration
│   ├── db-utils.ts         # Database utilities
│   ├── prisma.ts           # Prisma client
│   ├── task-router.ts      # Routing algorithms
│   ├── types.ts            # TypeScript types
│   └── utils.ts            # General utilities
├── prisma/                 # Database schema and migrations
│   ├── migrations/         # Database migrations
│   ├── schema.prisma       # Database schema
│   └── seed.ts             # Demo data seeding
├── types/                  # Type definitions
│   └── next-auth.d.ts      # NextAuth type extensions
├── docs/                   # Documentation
│   ├── phase1-implementation-summary.md
│   └── task_routing_architecture.md
└── middleware.ts           # Route protection middleware
```

## 🔐 Authentication & Security

### Authentication Flow
1. **Credential-based Login** - Email and password authentication
2. **JWT Sessions** - Secure session management with NextAuth.js
3. **Role-based Access** - Admin and Agent role separation
4. **Route Protection** - Middleware-based route guards
5. **Organization Isolation** - Multi-tenant data separation

### Security Features
- Password hashing with bcrypt
- CSRF protection via NextAuth.js
- SQL injection prevention with Prisma
- Input validation with Zod schemas
- Environment variable protection

## 📈 Performance & Monitoring

### Database Optimization
- Optimized indexes for common queries
- Efficient pagination for large datasets
- Connection pooling with Prisma
- Query optimization for dashboard statistics

### Monitoring Endpoints
- `/api/test-db` - Database connectivity test
- `/api/routing/stats` - System performance metrics
- Health checks for all core services

## 🧪 Testing

### Demo Data
The application includes comprehensive demo data:
- 1 Demo Organization
- 4 Users (1 Admin + 3 Agents)
- 4 Sample Tasks with different priorities and statuses
- Complete task assignment history

### Testing Scenarios
1. **Task Creation** - Create tasks and verify auto-assignment
2. **Manual Assignment** - Test different routing strategies
3. **Status Updates** - Update task and agent statuses
4. **Dashboard Metrics** - Verify real-time statistics
5. **Role-based Access** - Test admin vs agent permissions

## 🚀 Deployment

### Environment Variables
```env
# Database
DATABASE_URL="postgresql://..."

# Authentication
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="https://your-domain.com"

# Optional: Additional configuration
NODE_ENV="production"
```

### Deployment Platforms
- **Vercel** (Recommended) - Zero-config deployment
- **Railway** - Database and app hosting
- **Heroku** - Traditional PaaS deployment
- **Docker** - Containerized deployment

### Database Hosting
- **Neon** (Recommended) - Serverless PostgreSQL
- **Supabase** - Open-source Firebase alternative
- **PlanetScale** - MySQL-compatible serverless database
- **AWS RDS** - Managed relational database

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/amazing-feature`)
3. **Commit your changes** (`git commit -m 'Add amazing feature'`)
4. **Push to the branch** (`git push origin feature/amazing-feature`)
5. **Open a Pull Request**

### Development Guidelines
- Follow TypeScript best practices
- Use Prettier for code formatting
- Write meaningful commit messages
- Add tests for new features
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js** - React framework for production
- **Prisma** - Next-generation ORM for Node.js
- **shadcn/ui** - Beautiful and accessible UI components
- **NextAuth.js** - Complete authentication solution
- **Tailwind CSS** - Utility-first CSS framework

## 📞 Support

For support and questions:
- 📧 Email: <EMAIL>
- 📖 Documentation: [docs/](./docs/)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)

---

**Built with ❤️ for efficient team management and task distribution.**
