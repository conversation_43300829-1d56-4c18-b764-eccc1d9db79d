import { NextRequest, NextResponse } from 'next/server';
import { AvailabilityManager } from '../../../../../lib/availability-utils';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '../../../../../lib/prisma';
import { UserStatus } from '@prisma/client';

const availabilityManager = new AvailabilityManager();

interface Props {
  params: { id: string }
}

export async function GET(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Get current availability
    const currentAvailability = await availabilityManager.getCurrentAvailability(agentId);

    // Get scheduled changes
    const scheduledChanges = await prisma.agentAvailability.findMany({
      where: {
        agentId,
        isScheduled: true,
        endTime: {
          gt: new Date()
        }
      },
      orderBy: {
        startTime: 'asc'
      }
    });

    // Check if agent is within working hours
    const isWithinWorkingHours = await availabilityManager.isAgentAvailable(agentId);

    return NextResponse.json({
      currentAvailability,
      scheduledChanges,
      isWithinWorkingHours
    });
  } catch (error) {
    console.error('Error in GET /api/agents/[id]/availability:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { 
      status, 
      statusMessage,
      startTime,
      endTime
    } = body;

    // Validate status
    if (!Object.values(UserStatus).includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Handle scheduled availability
    if (startTime && endTime) {
      try {
        const scheduledAvailability = await availabilityManager.scheduleAvailability(
          agentId,
          status,
          new Date(startTime),
          new Date(endTime),
          statusMessage
        );

        return NextResponse.json(
          { availability: scheduledAvailability },
          { status: 201 }
        );
      } catch (error) {
        if (error instanceof Error && error.message === 'Schedule conflict detected') {
          return NextResponse.json(
            { error: 'Schedule conflict detected' },
            { status: 409 }
          );
        }
        throw error;
      }
    }

    // Update current availability
    const availability = await availabilityManager.updateAvailability(
      agentId,
      status,
      { statusMessage }
    );

    return NextResponse.json(
      { availability },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/agents/[id]/availability:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Get schedule ID from query params
    const { searchParams } = new URL(req.url);
    const scheduleId = searchParams.get('scheduleId');

    if (!scheduleId) {
      return NextResponse.json(
        { error: 'Schedule ID is required' },
        { status: 400 }
      );
    }

    // Delete scheduled availability
    await prisma.agentAvailability.deleteMany({
      where: {
        id: scheduleId,
        agentId,
        isScheduled: true,
        startTime: {
          gt: new Date()
        }
      }
    });

    return NextResponse.json(
      { message: 'Scheduled availability deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/agents/[id]/availability:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}