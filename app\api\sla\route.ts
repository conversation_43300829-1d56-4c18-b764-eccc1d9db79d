import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get organizationId from query params
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    // Get SLA policies
    const policies = await prisma.sLAPolicy.findMany({
      where: { organizationId }
    });

    return NextResponse.json({ policies });
  } catch (error) {
    console.error('Error in GET /api/sla:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const {
      organizationId,
      name,
      description,
      priority,
      responseTime,
      resolutionTime,
      escalationRules
    } = body;

    // Validate required fields
    if (!organizationId || !name || !priority || !responseTime || !resolutionTime) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create SLA policy
    const policy = await prisma.sLAPolicy.create({
      data: {
        organizationId,
        name,
        description,
        priority,
        responseTime,
        resolutionTime,
        escalationRules: escalationRules || [],
        isActive: true
      }
    });

    return NextResponse.json(
      { policy },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/sla:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}