import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { UpdateTaskRequest } from "@/lib/types";
import { TaskStatus, TaskEventType, Prisma } from "@prisma/client";
import { PerformanceManager } from "@/lib/performance-manager";

const performanceManager = new PerformanceManager();

// GET /api/tasks/[id] - Get task details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const task = await prisma.task.findUnique({
      where: { id: params.id },
      include: {
        assignedUser: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        requiredSkills: true,
        events: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "TASK_NOT_FOUND",
            message: "Task not found",
          },
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: task,
    });
  } catch (error) {
    console.error("Error fetching task:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "FETCH_TASK_FAILED",
          message: "Failed to fetch task",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      },
      { status: 500 }
    );
  }
}

// PATCH /api/tasks/[id] - Update task
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body: UpdateTaskRequest = await request.json();

    // Check if task exists
    const existingTask = await prisma.task.findUnique({
      where: { id: params.id },
      include: {
        assignedUser: true,
      },
    });

    if (!existingTask) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "TASK_NOT_FOUND",
            message: "Task not found",
          },
        },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: Prisma.TaskUpdateInput = {};
    const changes: Record<string, { from: unknown; to: unknown }> = {};

    if (body.title !== undefined) {
      updateData.title = body.title;
      changes.title = { from: existingTask.title, to: body.title };
    }

    if (body.description !== undefined) {
      updateData.description = body.description;
      changes.description = {
        from: existingTask.description,
        to: body.description,
      };
    }

    if (body.priority !== undefined) {
      updateData.priority = body.priority;
      changes.priority = { from: existingTask.priority, to: body.priority };
    }

    if (body.status !== undefined) {
      updateData.status = body.status;
      changes.status = { from: existingTask.status, to: body.status };
    }

    if (body.type !== undefined) {
      updateData.type = body.type;
      changes.type = { from: existingTask.type, to: body.type };
    }

    if (body.estimatedDuration !== undefined) {
      updateData.estimatedDuration = body.estimatedDuration;
      changes.estimatedDuration = {
        from: existingTask.estimatedDuration,
        to: body.estimatedDuration,
      };
    }

    // Handle assignment changes
    if (body.assignedTo !== undefined) {
      if (body.assignedTo === null) {
        updateData.assignedUser = { disconnect: true };
      } else {
        updateData.assignedUser = { connect: { id: body.assignedTo } };
      }
      changes.assignedTo = {
        from: existingTask.assignedTo,
        to: body.assignedTo,
      };

      // Update user task counts
      if (
        existingTask.assignedTo &&
        existingTask.assignedTo !== body.assignedTo
      ) {
        // Decrement old assignee's task count
        await prisma.user.update({
          where: { id: existingTask.assignedTo },
          data: { currentTaskCount: { decrement: 1 } },
        });
      }

      if (body.assignedTo && body.assignedTo !== existingTask.assignedTo) {
        // Increment new assignee's task count
        await prisma.user.update({
          where: { id: body.assignedTo },
          data: { currentTaskCount: { increment: 1 } },
        });
      }
    }

    // Prepare final update data with additional fields
    const finalUpdateData = {
      ...updateData,
      ...(body.assignedTo !== undefined && {
        assignedAt: body.assignedTo ? new Date() : null,
      }),
    };

    // Update task
    const updatedTask = await prisma.task.update({
      where: { id: params.id },
      data: finalUpdateData,
      include: {
        assignedUser: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Handle metrics updates for status changes
    if (body.status !== undefined && body.status !== existingTask.status) {
      if (body.status === TaskStatus.COMPLETED && existingTask.assignedTo) {
        await performanceManager.onTaskCompleted(params.id);
      }
    }

    // Create task event for the update
    await prisma.taskEvent.create({
      data: {
        taskId: params.id,
        type: TaskEventType.UPDATED,
        data: JSON.parse(JSON.stringify(changes)),
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedTask,
      message: "Task updated successfully",
    });
  } catch (error) {
    console.error("Error updating task:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "UPDATE_TASK_FAILED",
          message: "Failed to update task",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      },
      { status: 500 }
    );
  }
}

// DELETE /api/tasks/[id] - Delete task
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if task exists
    const existingTask = await prisma.task.findUnique({
      where: { id: params.id },
    });

    if (!existingTask) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: "TASK_NOT_FOUND",
            message: "Task not found",
          },
        },
        { status: 404 }
      );
    }

    // Update assigned user's task count if task was assigned
    if (existingTask.assignedTo) {
      await prisma.user.update({
        where: { id: existingTask.assignedTo },
        data: { currentTaskCount: { decrement: 1 } },
      });
    }

    // Delete task (this will cascade delete events due to schema)
    await prisma.task.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "Task deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting task:", error);
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "DELETE_TASK_FAILED",
          message: "Failed to delete task",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      },
      { status: 500 }
    );
  }
}
