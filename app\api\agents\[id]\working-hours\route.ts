import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { AvailabilityManager } from '@/lib/availability-utils';
import type { WorkHoursCreateInput } from '@/lib/types';

const availabilityManager = new AvailabilityManager();

interface Props {
  params: { id: string }
}

export async function GET(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Get agent's working hours
    const workingHours = await availabilityManager.getWorkingHours(agentId);

    return NextResponse.json({ workingHours });
  } catch (error) {
    console.error('Error in GET /api/agents/[id]/working-hours:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Parse request body
    const body = await req.json();
    const schedule: WorkHoursCreateInput[] = body.schedule;

    if (!Array.isArray(schedule)) {
      return NextResponse.json(
        { error: 'Schedule must be an array' },
        { status: 400 }
      );
    }

    // Set working hours
    const workingHours = await availabilityManager.setWorkingHours(
      agentId,
      schedule
    );

    return NextResponse.json(
      { workingHours },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/agents/[id]/working-hours:', error);
    
    // Handle validation errors
    if (error instanceof Error) {
      if (
        error.message.includes('Invalid day') ||
        error.message.includes('Invalid time') ||
        error.message.includes('Duplicate days')
      ) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { dayOfWeek, updates } = body;

    if (typeof dayOfWeek !== 'number' || !updates) {
      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }

    // Get current schedule
    const currentSchedule = await availabilityManager.getWorkingHours(agentId);
    
    // Update specific day
    const updatedSchedule = currentSchedule.map(day => {
      if (day.dayOfWeek === dayOfWeek) {
        return {
          ...day,
          ...updates,
          dayOfWeek // Ensure dayOfWeek can't be changed
        };
      }
      return day;
    });

    // Set updated schedule
    const workingHours = await availabilityManager.setWorkingHours(
      agentId,
      updatedSchedule.map(day => ({
        dayOfWeek: day.dayOfWeek,
        startTime: day.startTime,
        endTime: day.endTime,
        timeZone: day.timeZone,
        isWorkDay: day.isWorkDay
      }))
    );

    return NextResponse.json({ workingHours });
  } catch (error) {
    console.error('Error in PATCH /api/agents/[id]/working-hours:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Delete all working hours (reset schedule)
    await availabilityManager.setWorkingHours(agentId, []);

    return NextResponse.json(
      { message: 'Working hours reset successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/agents/[id]/working-hours:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}