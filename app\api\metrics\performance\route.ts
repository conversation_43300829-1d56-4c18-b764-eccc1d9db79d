import { NextResponse } from 'next/server'
import { z } from 'zod'
import { AnalyticsEngine } from '@/lib/analytics-engine'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

const querySchema = z.object({
  agentId: z.string(),
  period: z.string().optional().default('30d'),
  metricType: z.enum(['efficiency', 'quality', 'sla']).optional()
})

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Extract and validate query parameters
    const url = new URL(request.url)
    const queryParams = {
      agentId: url.searchParams.get('agentId'),
      period: url.searchParams.get('period'),
      metricType: url.searchParams.get('metricType')
    }

    const validatedParams = querySchema.safeParse(queryParams)
    if (!validatedParams.success) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: validatedParams.error.format() },
        { status: 400 }
      )
    }

    const analytics = new AnalyticsEngine()
    const metrics = await analytics.getAgentPerformanceSummary(
      validatedParams.data.agentId,
      validatedParams.data.period
    )

    // Filter metrics by type if specified
    if (validatedParams.data.metricType) {
      const filteredMetrics = {
        agentId: metrics.agentId,
        agentName: metrics.agentName,
        period: metrics.period,
        [validatedParams.data.metricType]: metrics[validatedParams.data.metricType]
      }
      return NextResponse.json(filteredMetrics)
    }

    return NextResponse.json(metrics)
  } catch (error) {
    console.error('Performance metrics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch performance metrics' },
      { status: 500 }
    )
  }
}