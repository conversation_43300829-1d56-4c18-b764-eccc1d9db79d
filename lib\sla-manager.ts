import { prisma } from './prisma';
import { Task, TaskStatus, TaskPriority } from '@prisma/client';
import { AvailabilityManager } from './availability-utils';

// SLA Types
export interface SLAPolicy {
  id: string;
  organizationId: string;
  name: string;
  description?: string | null;
  priority: string;
  responseTime: number;
  resolutionTime: number;
  escalationRules: EscalationRule[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface EscalationRule {
  level: number;
  escalateTo?: string;
  actions: EscalationAction[];
}

export interface EscalationAction {
  type: 'notify' | 'reassign' | 'update_priority';
  assignTo?: string;
  priority?: TaskPriority;
  notifyUsers?: string[];
}

export interface SLAStatus {
  taskId: string;
  responseBreached: boolean;
  resolutionBreached: boolean;
  currentEscalationLevel: number;
  breachedSLA: boolean;
  responseRemaining: number | null;
  resolutionRemaining: number | null;
}

export class SLAManager {
  private availabilityManager: AvailabilityManager;

  constructor() {
    this.availabilityManager = new AvailabilityManager();
  }

  // SLA Policy Management
  async applySLAPolicy(taskId: string, policyId: string): Promise<Task> {
    const policy = await prisma.sLAPolicy.findUnique({
      where: { id: policyId }
    });

    if (!policy) {
      throw new Error('SLA Policy not found');
    }

    // Calculate deadlines based on policy
    const now = new Date();
    const responseBy = this.calculateDeadline(now, policy.responseTime);
    const resolveBy = this.calculateDeadline(now, policy.resolutionTime);

    // Apply SLA policy to task
    return await prisma.task.update({
      where: { id: taskId },
      data: {
        slaPolicyId: policyId,
        responseBy,
        resolveBy,
        priority: policy.priority as TaskPriority
      }
    });
  }

  // Deadline Calculations
  private calculateDeadline(startTime: Date, minutes: number): Date {
    const deadline = new Date(startTime);
    const remainingMinutes = minutes;

    // TODO: Consider working hours and holidays using availabilityManager
    // For now, just add the minutes directly
    deadline.setMinutes(deadline.getMinutes() + remainingMinutes);
    return deadline;
  }

  // SLA Status Check
  async checkSLAStatus(taskId: string): Promise<SLAStatus> {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        slaPolicy: true,
        escalations: {
          where: { status: 'active' },
          orderBy: { level: 'desc' },
          take: 1
        }
      }
    });

    if (!task || !task.slaPolicy) {
      throw new Error('Task or SLA policy not found');
    }

    const now = new Date();
    const responseBreached = task.responseBy && !task.respondedAt && now > task.responseBy;
    const resolutionBreached = task.resolveBy && !task.resolvedAt && now > task.resolveBy;

    // Update SLA breach status if needed
    if ((responseBreached || resolutionBreached) && !task.breachedSLA) {
      await prisma.task.update({
        where: { id: taskId },
        data: { breachedSLA: true }
      });
    }

    return {
      taskId: task.id,
      responseBreached: responseBreached ?? false,
      resolutionBreached: resolutionBreached ?? false,
      currentEscalationLevel: task.escalations[0]?.level || 0,
      breachedSLA: (responseBreached ?? false) || (resolutionBreached ?? false),
      responseRemaining: task.responseBy ? this.calculateTimeRemaining(now, task.responseBy) : null,
      resolutionRemaining: task.resolveBy ? this.calculateTimeRemaining(now, task.resolveBy) : null
    };
  }

  // Escalation Management
  async processEscalations(): Promise<void> {
    const tasks = await prisma.task.findMany({
      where: {
        status: { not: TaskStatus.COMPLETED },
        slaPolicy: { isNot: null }
      },
      include: {
        slaPolicy: true,
        escalations: {
          where: { status: 'active' },
          orderBy: { level: 'desc' },
          take: 1
        }
      }
    });

    for (const task of tasks) {
      try {
        const status = await this.checkSLAStatus(task.id);
        
        if (status.breachedSLA) {
          await this.handleSLABreach(task, status);
        }
      } catch (error) {
        console.error(`Error processing escalations for task ${task.id}:`, error);
      }
    }
  }

  private async handleSLABreach(task: Task, status: SLAStatus): Promise<void> {
    if (!task.slaPolicyId) return;

    const policy = await prisma.sLAPolicy.findUnique({
      where: { id: task.slaPolicyId }
    });

    if (!policy) return;

    const escalationRules = policy.escalationRules as unknown as EscalationRule[];
    const currentLevel = status.currentEscalationLevel;

    // Find next applicable escalation rule
    const nextRule = escalationRules.find(rule => rule.level === currentLevel + 1);
    if (!nextRule) return;

    // Create new escalation
    const escalation = await prisma.escalation.create({
      data: {
        taskId: task.id,
        level: nextRule.level,
        reason: status.responseBreached ? 'response_time' : 'resolution_time',
        status: 'active',
        escalatedTo: nextRule.escalateTo
      }
    });

    // Execute escalation actions
    await this.executeEscalationActions(task, escalation, nextRule.actions);
  }

  private async executeEscalationActions(
    task: Task,
    escalation: { id: string; taskId: string; level: number; status: string },
    actions: EscalationAction[]
  ): Promise<void> {
    for (const action of actions) {
      try {
        switch (action.type) {
          case 'notify':
            // TODO: Implement notification system integration
            break;

          case 'reassign':
            if (action.assignTo) {
              await prisma.task.update({
                where: { id: task.id },
                data: { assignedTo: action.assignTo }
              });
            }
            break;

          case 'update_priority':
            if (action.priority) {
              await prisma.task.update({
                where: { id: task.id },
                data: { priority: action.priority }
              });
            }
            break;

          default:
            console.warn(`Unknown escalation action type: ${action.type}`);
        }
      } catch (error) {
        console.error(`Error executing escalation action:`, error);
      }
    }
  }

  // Helper Methods
  private calculateTimeRemaining(now: Date, deadline: Date): number {
    return Math.max(0, deadline.getTime() - now.getTime()) / 1000 / 60; // minutes
  }

  // Resolution Management
  async markResponded(taskId: string): Promise<Task> {
    return await prisma.task.update({
      where: { id: taskId },
      data: {
        respondedAt: new Date(),
        // Close any response time escalations
        escalations: {
          updateMany: {
            where: { 
              status: 'active',
              reason: 'response_time'
            },
            data: {
              status: 'resolved',
              resolvedAt: new Date()
            }
          }
        }
      }
    });
  }

  async markResolved(taskId: string): Promise<Task> {
    return await prisma.task.update({
      where: { id: taskId },
      data: {
        resolvedAt: new Date(),
        status: TaskStatus.COMPLETED,
        // Close all active escalations
        escalations: {
          updateMany: {
            where: { status: 'active' },
            data: {
              status: 'resolved',
              resolvedAt: new Date()
            }
          }
        }
      }
    });
  }
}