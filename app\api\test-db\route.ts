import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Test database connection and get basic stats
    const [organizationCount, userCount, taskCount, eventCount] = await Promise.all([
      prisma.organization.count(),
      prisma.user.count(),
      prisma.task.count(),
      prisma.taskEvent.count(),
    ])

    // Get sample data
    const [organizations, users, tasks] = await Promise.all([
      prisma.organization.findMany({
        include: {
          _count: {
            select: {
              users: true,
              tasks: true,
            },
          },
        },
      }),
      prisma.user.findMany({
        include: {
          organization: {
            select: {
              name: true,
            },
          },
          _count: {
            select: {
              assignedTasks: true,
            },
          },
        },
      }),
      prisma.task.findMany({
        include: {
          assignedUser: {
            select: {
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 5,
      }),
    ])

    return NextResponse.json({
      success: true,
      message: 'Database connection successful!',
      stats: {
        organizations: organizationCount,
        users: userCount,
        tasks: taskCount,
        events: eventCount,
      },
      data: {
        organizations,
        users,
        recentTasks: tasks,
      },
    })
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Database connection failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
