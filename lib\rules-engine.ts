import { prisma } from './prisma';
import {
  RuleCondition,
  RuleAction,
  TaskWithSkills,
  RuleExecutionResult,
  RuleContext
} from './types';
import { SkillsManager } from './skills-utils';
import { AvailabilityManager } from './availability-utils';
import { JsonValue, InputJsonValue } from '@prisma/client/runtime/library';
import { TaskPriority, Prisma } from '@prisma/client';

export class RulesEngine {
  private skillsManager: SkillsManager;
  private availabilityManager: AvailabilityManager;

  constructor() {
    this.skillsManager = new SkillsManager();
    this.availabilityManager = new AvailabilityManager();
  }

  // Core rule evaluation
  async evaluateRules(context: RuleContext): Promise<RuleExecutionResult[]> {
    const { task, organizationId } = context;

    // Get active rules for organization, ordered by priority
    const rules = await prisma.routingRule.findMany({
      where: {
        organizationId,
        isActive: true
      },
      orderBy: {
        priority: 'desc'
      }
    });

    const results: RuleExecutionResult[] = [];

    for (const rule of rules) {
      try {
        // Evaluate conditions
        const conditions = rule.conditions as unknown as RuleCondition[];
        const conditionsMatch = await this.evaluateConditions(conditions, context);
        
        if (conditionsMatch) {
          // Execute actions if conditions match
          const actions = rule.actions as unknown as RuleAction[];
          const actionResults = await this.executeActions(actions, context);
          
          results.push({
            ruleId: rule.id,
            ruleName: rule.name,
            succeeded: true,
            conditionsMatched: true,
            actionsExecuted: actionResults
          });

          // Record rule execution
          await this.recordRuleExecution(rule.id, task.id, true, {
            conditionsMatched: true,
            actionResults
          });
        } else {
          results.push({
            ruleId: rule.id,
            ruleName: rule.name,
            succeeded: true,
            conditionsMatched: false,
            actionsExecuted: []
          });
        }
      } catch (error) {
        console.error(`Error evaluating rule ${rule.id}:`, error);
        
        results.push({
          ruleId: rule.id,
          ruleName: rule.name,
          succeeded: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });

        // Record failed execution
        await this.recordRuleExecution(rule.id, task.id, false, {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  // Condition evaluation
  private async evaluateConditions(conditions: RuleCondition[], context: RuleContext): Promise<boolean> {
    for (const condition of conditions) {
      const matches = await this.evaluateCondition(condition, context);
      if (!matches) return false; // All conditions must match
    }
    return true;
  }

  private async evaluateCondition(condition: RuleCondition, context: RuleContext): Promise<boolean> {
    const { task } = context;

    switch (condition.type) {
      case 'skill_required':
        return this.evaluateSkillCondition(condition, task);
      
      case 'priority':
        return task.priority === condition.value;
      
      case 'workload':
        return this.evaluateWorkloadCondition(condition);
      
      case 'time_of_day':
        return this.evaluateTimeCondition(condition);
      
      case 'agent_performance':
        return this.evaluatePerformanceCondition(condition);
      
      default:
        throw new Error(`Unknown condition type: ${condition.type}`);
    }
  }

  // Action execution
  private async executeActions(actions: RuleAction[], context: RuleContext): Promise<string[]> {
    const executedActions: string[] = [];
    
    for (const action of actions) {
      await this.executeAction(action, context);
      executedActions.push(`${action.type}:${action.value}`);
    }

    return executedActions;
  }

  private async executeAction(action: RuleAction, context: RuleContext): Promise<void> {
    const { task } = context;

    switch (action.type) {
      case 'set_strategy':
        await this.updateRoutingStrategy(task.id, String(action.value));
        break;

      case 'require_min_performance':
        await this.setPerformanceRequirement(task.id, Number(action.value));
        break;

      case 'set_priority':
        await this.updateTaskPriority(task.id, String(action.value) as TaskPriority);
        break;

      case 'add_tag':
        await this.addTaskTag(task.id, String(action.value));
        break;

      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  // Condition evaluation helpers
  private async evaluateSkillCondition(condition: RuleCondition, task: TaskWithSkills): Promise<boolean> {
    const requiredSkill = task.requiredSkills.find(s => s.skillName === condition.skill);
    if (!requiredSkill) return false;
    return requiredSkill.requiredLevel >= (condition.minLevel || 1);
  }

  private async evaluateWorkloadCondition(condition: RuleCondition): Promise<boolean> {
    const { threshold = 0.8 } = condition;
    const agents = await prisma.user.findMany({
      where: { role: 'AGENT' },
      select: {
        id: true,
        currentTaskCount: true,
        maxConcurrentTasks: true
      }
    });

    const avgWorkload = agents.reduce((sum, agent) => {
      return sum + (agent.currentTaskCount / agent.maxConcurrentTasks);
    }, 0) / agents.length;

    return avgWorkload >= threshold;
  }

  private evaluateTimeCondition(condition: RuleCondition): boolean {
    const { startTime = '00:00', endTime = '23:59' } = condition;
    const now = new Date();
    const currentHour = now.getHours();
    const [startHour] = startTime.split(':').map(Number);
    const [endHour] = endTime.split(':').map(Number);

    return currentHour >= startHour && currentHour < endHour;
  }

  private async evaluatePerformanceCondition(condition: RuleCondition): Promise<boolean> {
    const { metric, threshold } = condition;
    const performances = await prisma.agentPerformance.findMany();

    const thresholdValue = threshold || 0;
    
    switch (metric) {
      case 'completion_rate':
        return performances.some(p => p.completionRate >= thresholdValue);
      case 'quality_score':
        return performances.some(p => p.qualityScore >= thresholdValue);
      default:
        return false;
    }
  }

  // Action execution helpers
  private async updateRoutingStrategy(taskId: string, strategy: string): Promise<void> {
    await prisma.task.update({
      where: { id: taskId },
      data: {
        metadata: {
          routingStrategy: strategy
        }
      }
    });
  }

  private async setPerformanceRequirement(taskId: string, minScore: number): Promise<void> {
    await prisma.task.update({
      where: { id: taskId },
      data: {
        metadata: {
          minPerformanceScore: minScore
        }
      }
    });
  }

  private async updateTaskPriority(taskId: string, priority: string): Promise<void> {
    await prisma.task.update({
      where: { id: taskId },
      data: {
        priority: priority as TaskPriority
      }
    });
  }

  private async addTaskTag(taskId: string, tag: string): Promise<void> {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      select: { metadata: true }
    });

    const metadata = task?.metadata as Prisma.JsonObject || {};
    const currentTags = (metadata.tags as string[]) || [];
    const uniqueTags = Array.from(new Set([...currentTags, tag]));
    
    const updatedMetadata: Prisma.JsonObject = {
      ...metadata,
      tags: uniqueTags
    };
    
    await prisma.task.update({
      where: { id: taskId },
      data: {
        metadata: updatedMetadata as InputJsonValue
      }
    });
  }

  // Record rule execution
  private async recordRuleExecution(
    ruleId: string,
    taskId: string,
    succeeded: boolean,
    details: Record<string, JsonValue>
  ): Promise<void> {
    await prisma.ruleExecution.create({
      data: {
        ruleId,
        taskId,
        succeeded,
        details
      }
    });
  }
}