import { NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

const ratingSchema = z.object({
  rating: z.number().min(1).max(5),
  feedback: z.string().optional(),
  category: z.enum(['satisfaction', 'quality', 'speed']).optional()
})

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const taskId = params.id
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: { rating: true }
    })

    if (!task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      )
    }

    // Check if user has access to this task's organization
    if (session.user.organizationId !== task.organizationId) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    const body = await request.json()
    const validatedData = ratingSchema.safeParse(body)

    if (!validatedData.success) {
      return NextResponse.json(
        { error: 'Invalid rating data', details: validatedData.error.format() },
        { status: 400 }
      )
    }

    const rating = await prisma.taskRating.upsert({
      where: {
        taskId
      },
      update: {
        ...validatedData.data,
        ratedBy: session.user.id
      },
      create: {
        taskId,
        ...validatedData.data,
        ratedBy: session.user.id
      }
    })

    return NextResponse.json(rating)
  } catch (error) {
    console.error('Task rating error:', error)
    return NextResponse.json(
      { error: 'Failed to save task rating' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const taskId = params.id
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: { rating: true }
    })

    if (!task) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      )
    }

    // Check if user has access to this task's organization
    if (session.user.organizationId !== task.organizationId) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    if (!task.rating) {
      return NextResponse.json(
        { error: 'No rating found for this task' },
        { status: 404 }
      )
    }

    return NextResponse.json(task.rating)
  } catch (error) {
    console.error('Task rating error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch task rating' },
      { status: 500 }
    )
  }
}