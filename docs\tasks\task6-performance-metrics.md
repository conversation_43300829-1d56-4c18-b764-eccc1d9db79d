# Task 6: Performance Metrics & Analytics

## 🎯 Objective

Implement comprehensive performance tracking and analytics system that monitors agent performance, system efficiency, and provides actionable insights for optimization.

## 📋 Status

- **Priority**: P2 (Important)
- **Status**: ⏳ Waiting for Task 5
- **Estimated Time**: 5-7 hours
- **Dependencies**: Task 5 (SLA & Escalation System)

## 🔧 Technical Requirements

### Database Schema Updates

```prisma
model PerformanceMetric {
  id             String   @id @default(cuid())
  agentId        String   @map("agent_id")
  organizationId String   @map("organization_id")
  metricType     String   @map("metric_type") // 'daily', 'weekly', 'monthly'
  periodStart    DateTime @map("period_start")
  periodEnd      DateTime @map("period_end")
  
  // Task metrics
  tasksAssigned     Int @default(0) @map("tasks_assigned")
  tasksCompleted    Int @default(0) @map("tasks_completed")
  tasksEscalated    Int @default(0) @map("tasks_escalated")
  
  // Time metrics (in minutes)
  avgResponseTime   Float @default(0) @map("avg_response_time")
  avgResolutionTime Float @default(0) @map("avg_resolution_time")
  totalWorkTime     Int   @default(0) @map("total_work_time")
  
  // Quality metrics
  qualityScore      Float @default(0) @map("quality_score")
  customerRating    Float @default(0) @map("customer_rating")
  
  // SLA metrics
  slaResponseMet    Int @default(0) @map("sla_response_met")
  slaResolutionMet  Int @default(0) @map("sla_resolution_met")
  slaResponseTotal  Int @default(0) @map("sla_response_total")
  slaResolutionTotal Int @default(0) @map("sla_resolution_total")
  
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  agent        User         @relation(fields: [agentId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@unique([agentId, metricType, periodStart])
  @@map("performance_metrics")
}

model SystemMetric {
  id             String   @id @default(cuid())
  organizationId String   @map("organization_id")
  metricType     String   @map("metric_type") // 'hourly', 'daily', 'weekly'
  timestamp      DateTime
  
  // System performance
  totalTasks        Int @default(0) @map("total_tasks")
  tasksRouted       Int @default(0) @map("tasks_routed")
  routingFailures   Int @default(0) @map("routing_failures")
  avgRoutingTime    Float @default(0) @map("avg_routing_time")
  
  // Load balancing
  agentsActive      Int @default(0) @map("agents_active")
  agentsIdle        Int @default(0) @map("agents_idle")
  avgUtilization    Float @default(0) @map("avg_utilization")
  
  // SLA system-wide
  slaCompliance     Float @default(0) @map("sla_compliance")
  escalationRate    Float @default(0) @map("escalation_rate")
  
  createdAt DateTime @default(now()) @map("created_at")
  
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@unique([organizationId, metricType, timestamp])
  @@map("system_metrics")
}

model TaskRating {
  id         String   @id @default(cuid())
  taskId     String   @unique @map("task_id")
  rating     Int      // 1-5 scale
  feedback   String?
  ratedBy    String   @map("rated_by") // Customer/requester ID
  ratedAt    DateTime @default(now()) @map("rated_at")
  
  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  
  @@map("task_ratings")
}
```

### TypeScript Types

```typescript
export interface PerformanceMetrics {
  id: string;
  agentId: string;
  organizationId: string;
  metricType: 'daily' | 'weekly' | 'monthly';
  periodStart: Date;
  periodEnd: Date;
  
  // Task metrics
  tasksAssigned: number;
  tasksCompleted: number;
  tasksEscalated: number;
  
  // Time metrics
  avgResponseTime: number;
  avgResolutionTime: number;
  totalWorkTime: number;
  
  // Quality metrics
  qualityScore: number;
  customerRating: number;
  
  // SLA metrics
  slaResponseMet: number;
  slaResolutionMet: number;
  slaResponseTotal: number;
  slaResolutionTotal: number;
}

export interface SystemMetrics {
  id: string;
  organizationId: string;
  metricType: 'hourly' | 'daily' | 'weekly';
  timestamp: Date;
  
  totalTasks: number;
  tasksRouted: number;
  routingFailures: number;
  avgRoutingTime: number;
  
  agentsActive: number;
  agentsIdle: number;
  avgUtilization: number;
  
  slaCompliance: number;
  escalationRate: number;
}

export interface AgentPerformanceSummary {
  agentId: string;
  agentName: string;
  period: string;
  
  efficiency: {
    completionRate: number;
    avgResponseTime: number;
    avgResolutionTime: number;
    utilization: number;
  };
  
  quality: {
    qualityScore: number;
    customerRating: number;
    escalationRate: number;
  };
  
  sla: {
    responseCompliance: number;
    resolutionCompliance: number;
  };
  
  trends: {
    completionRateTrend: number;
    qualityTrend: number;
    efficiencyTrend: number;
  };
}
```

## 🛠️ Implementation Steps

### Step 1: Database Schema Updates (30 minutes)

1. **Update Prisma Schema**
   - Add performance metrics, system metrics, and task rating models
   - Update relations with existing models

2. **Create Migration**
   ```bash
   npx prisma migrate dev --name add-performance-metrics
   ```

3. **Update Seed Data**
   - Add sample performance data for demo agents
   - Create historical metrics for testing

### Step 2: Metrics Collection System (120 minutes)

1. **Create `lib/metrics-collector.ts`**
   ```typescript
   export class MetricsCollector {
     // Agent performance metrics
     async collectAgentMetrics(agentId: string, period: 'daily' | 'weekly' | 'monthly'): Promise<PerformanceMetrics> {
       const { start, end } = this.getPeriodRange(period);
       
       // Get task data for the period
       const tasks = await prisma.task.findMany({
         where: {
           assignedTo: agentId,
           createdAt: { gte: start, lte: end }
         },
         include: {
           sla: true,
           events: true,
           rating: true
         }
       });
       
       const metrics = this.calculateAgentMetrics(tasks, agentId, period, start, end);
       
       // Store or update metrics
       return await this.storeAgentMetrics(metrics);
     }
     
     calculateAgentMetrics(tasks: Task[], agentId: string, period: string, start: Date, end: Date): PerformanceMetrics {
       const completedTasks = tasks.filter(t => t.status === 'completed');
       const escalatedTasks = tasks.filter(t => t.status === 'escalated');
       
       // Calculate response times
       const responseTimes = tasks
         .filter(t => t.sla?.firstResponseAt)
         .map(t => {
           const responseTime = t.sla.firstResponseAt.getTime() - t.createdAt.getTime();
           return responseTime / (1000 * 60); // Convert to minutes
         });
       
       // Calculate resolution times
       const resolutionTimes = completedTasks
         .filter(t => t.sla?.resolvedAt)
         .map(t => {
           const resolutionTime = t.sla.resolvedAt.getTime() - t.createdAt.getTime();
           return resolutionTime / (1000 * 60); // Convert to minutes
         });
       
       // Calculate quality metrics
       const ratings = tasks.filter(t => t.rating).map(t => t.rating.rating);
       const avgRating = ratings.length > 0 ? ratings.reduce((a, b) => a + b, 0) / ratings.length : 0;
       
       // Calculate SLA compliance
       const slaResponseMet = tasks.filter(t => t.sla && !t.sla.responseBreached).length;
       const slaResolutionMet = tasks.filter(t => t.sla && !t.sla.resolutionBreached).length;
       
       return {
         agentId,
         organizationId: tasks[0]?.organizationId || '',
         metricType: period,
         periodStart: start,
         periodEnd: end,
         
         tasksAssigned: tasks.length,
         tasksCompleted: completedTasks.length,
         tasksEscalated: escalatedTasks.length,
         
         avgResponseTime: responseTimes.length > 0 ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0,
         avgResolutionTime: resolutionTimes.length > 0 ? resolutionTimes.reduce((a, b) => a + b, 0) / resolutionTimes.length : 0,
         totalWorkTime: this.calculateWorkTime(tasks),
         
         qualityScore: this.calculateQualityScore(tasks),
         customerRating: avgRating,
         
         slaResponseMet,
         slaResolutionMet,
         slaResponseTotal: tasks.filter(t => t.sla).length,
         slaResolutionTotal: tasks.filter(t => t.sla).length
       };
     }
     
     // System metrics collection
     async collectSystemMetrics(organizationId: string, period: 'hourly' | 'daily' | 'weekly'): Promise<SystemMetrics> {
       const { start, end } = this.getPeriodRange(period);
       
       // Get system-wide data
       const tasks = await prisma.task.findMany({
         where: {
           organizationId,
           createdAt: { gte: start, lte: end }
         },
         include: { sla: true }
       });
       
       const agents = await prisma.user.findMany({
         where: { organizationId, role: 'AGENT' }
       });
       
       const metrics = this.calculateSystemMetrics(tasks, agents, organizationId, period, end);
       
       return await this.storeSystemMetrics(metrics);
     }
     
     calculateSystemMetrics(tasks: Task[], agents: User[], organizationId: string, period: string, timestamp: Date): SystemMetrics {
       const routedTasks = tasks.filter(t => t.assignedTo);
       const failedRouting = tasks.filter(t => !t.assignedTo && t.status === 'pending');
       
       const activeAgents = agents.filter(a => a.status === 'available' || a.currentTaskCount > 0);
       const idleAgents = agents.filter(a => a.status === 'available' && a.currentTaskCount === 0);
       
       const totalCapacity = agents.reduce((sum, a) => sum + a.maxConcurrentTasks, 0);
       const currentLoad = agents.reduce((sum, a) => sum + a.currentTaskCount, 0);
       const utilization = totalCapacity > 0 ? (currentLoad / totalCapacity) * 100 : 0;
       
       // SLA compliance
       const slaCompliantTasks = tasks.filter(t => t.sla && !t.sla.responseBreached && !t.sla.resolutionBreached);
       const slaCompliance = tasks.length > 0 ? (slaCompliantTasks.length / tasks.length) * 100 : 0;
       
       const escalatedTasks = tasks.filter(t => t.status === 'escalated');
       const escalationRate = tasks.length > 0 ? (escalatedTasks.length / tasks.length) * 100 : 0;
       
       return {
         organizationId,
         metricType: period,
         timestamp,
         
         totalTasks: tasks.length,
         tasksRouted: routedTasks.length,
         routingFailures: failedRouting.length,
         avgRoutingTime: this.calculateAvgRoutingTime(routedTasks),
         
         agentsActive: activeAgents.length,
         agentsIdle: idleAgents.length,
         avgUtilization: utilization,
         
         slaCompliance,
         escalationRate
       };
     }
   }
   ```

### Step 3: Analytics Engine (90 minutes)

1. **Create `lib/analytics-engine.ts`**
   ```typescript
   export class AnalyticsEngine {
     // Agent performance analysis
     async getAgentPerformanceSummary(agentId: string, period: string = '30d'): Promise<AgentPerformanceSummary> {
       const metrics = await this.getAgentMetrics(agentId, period);
       const previousMetrics = await this.getAgentMetrics(agentId, this.getPreviousPeriod(period));
       
       return {
         agentId,
         agentName: await this.getAgentName(agentId),
         period,
         
         efficiency: {
           completionRate: this.calculateCompletionRate(metrics),
           avgResponseTime: metrics.avgResponseTime,
           avgResolutionTime: metrics.avgResolutionTime,
           utilization: this.calculateUtilization(metrics)
         },
         
         quality: {
           qualityScore: metrics.qualityScore,
           customerRating: metrics.customerRating,
           escalationRate: this.calculateEscalationRate(metrics)
         },
         
         sla: {
           responseCompliance: this.calculateSLACompliance(metrics.slaResponseMet, metrics.slaResponseTotal),
           resolutionCompliance: this.calculateSLACompliance(metrics.slaResolutionMet, metrics.slaResolutionTotal)
         },
         
         trends: {
           completionRateTrend: this.calculateTrend(metrics, previousMetrics, 'completionRate'),
           qualityTrend: this.calculateTrend(metrics, previousMetrics, 'qualityScore'),
           efficiencyTrend: this.calculateTrend(metrics, previousMetrics, 'efficiency')
         }
       };
     }
     
     // Team performance analysis
     async getTeamPerformanceAnalysis(organizationId: string, period: string = '30d'): Promise<any> {
       const agents = await prisma.user.findMany({
         where: { organizationId, role: 'AGENT' }
       });
       
       const teamMetrics = await Promise.all(
         agents.map(agent => this.getAgentPerformanceSummary(agent.id, period))
       );
       
       return {
         teamSize: agents.length,
         totalTasks: teamMetrics.reduce((sum, m) => sum + m.efficiency.completionRate, 0),
         avgCompletionRate: this.calculateTeamAverage(teamMetrics, 'efficiency.completionRate'),
         avgQualityScore: this.calculateTeamAverage(teamMetrics, 'quality.qualityScore'),
         avgSLACompliance: this.calculateTeamAverage(teamMetrics, 'sla.responseCompliance'),
         
         topPerformers: this.getTopPerformers(teamMetrics, 3),
         improvementOpportunities: this.getImprovementOpportunities(teamMetrics),
         
         distributionAnalysis: {
           workloadDistribution: this.analyzeWorkloadDistribution(teamMetrics),
           skillUtilization: await this.analyzeSkillUtilization(organizationId, period),
           performanceVariance: this.calculatePerformanceVariance(teamMetrics)
         }
       };
     }
     
     // System performance analysis
     async getSystemPerformanceAnalysis(organizationId: string, period: string = '7d'): Promise<any> {
       const systemMetrics = await this.getSystemMetrics(organizationId, period);
       
       return {
         overview: {
           totalTasks: systemMetrics.reduce((sum, m) => sum + m.totalTasks, 0),
           routingSuccessRate: this.calculateRoutingSuccessRate(systemMetrics),
           avgUtilization: this.calculateAvgUtilization(systemMetrics),
           slaCompliance: this.calculateAvgSLACompliance(systemMetrics)
         },
         
         trends: {
           taskVolumeTrend: this.calculateTaskVolumeTrend(systemMetrics),
           utilizationTrend: this.calculateUtilizationTrend(systemMetrics),
           slaComplianceTrend: this.calculateSLAComplianceTrend(systemMetrics)
         },
         
         insights: {
           peakHours: this.identifyPeakHours(systemMetrics),
           bottlenecks: this.identifyBottlenecks(systemMetrics),
           recommendations: this.generateRecommendations(systemMetrics)
         }
       };
     }
     
     // Predictive analytics
     async getPredictiveInsights(organizationId: string): Promise<any> {
       const historicalData = await this.getHistoricalMetrics(organizationId, '90d');
       
       return {
         workloadForecast: this.forecastWorkload(historicalData),
         capacityRecommendations: this.recommendCapacityChanges(historicalData),
         slaRiskPrediction: this.predictSLARisks(historicalData),
         performanceProjections: this.projectPerformanceChanges(historicalData)
       };
     }
   }
   ```

### Step 4: Metrics Automation (60 minutes)

1. **Create `lib/metrics-scheduler.ts`**
   ```typescript
   export class MetricsScheduler {
     private intervals: Map<string, NodeJS.Timeout> = new Map();
     
     start(): void {
       // Daily metrics collection
       this.scheduleDaily('daily-metrics', async () => {
         await this.collectDailyMetrics();
       });
       
       // Weekly metrics collection
       this.scheduleWeekly('weekly-metrics', async () => {
         await this.collectWeeklyMetrics();
       });
       
       // Hourly system metrics
       this.scheduleHourly('system-metrics', async () => {
         await this.collectSystemMetrics();
       });
     }
     
     async collectDailyMetrics(): Promise<void> {
       const organizations = await prisma.organization.findMany();
       const collector = new MetricsCollector();
       
       for (const org of organizations) {
         const agents = await prisma.user.findMany({
           where: { organizationId: org.id, role: 'AGENT' }
         });
         
         for (const agent of agents) {
           await collector.collectAgentMetrics(agent.id, 'daily');
         }
       }
     }
     
     async collectWeeklyMetrics(): Promise<void> {
       // Similar to daily but for weekly periods
     }
     
     async collectSystemMetrics(): Promise<void> {
       const organizations = await prisma.organization.findMany();
       const collector = new MetricsCollector();
       
       for (const org of organizations) {
         await collector.collectSystemMetrics(org.id, 'hourly');
       }
     }
   }
   ```

### Step 5: API Endpoints (75 minutes)

1. **Performance Metrics API** - `app/api/metrics/performance/route.ts`
   - GET: Get agent performance metrics
   - Query parameters: agentId, period, metricType

2. **Team Analytics API** - `app/api/analytics/team/route.ts`
   - GET: Get team performance analysis
   - Query parameters: organizationId, period

3. **System Metrics API** - `app/api/metrics/system/route.ts`
   - GET: Get system performance metrics
   - Query parameters: organizationId, period, metricType

4. **Task Rating API** - `app/api/tasks/[id]/rating/route.ts`
   - POST: Submit task rating
   - GET: Get task rating

5. **Predictive Analytics API** - `app/api/analytics/predictions/route.ts`
   - GET: Get predictive insights and recommendations

### Step 6: Real-time Metrics Updates (45 minutes)

1. **Update Task Event Handlers**
   ```typescript
   // When task is completed
   async function onTaskCompleted(taskId: string) {
     // Update real-time metrics
     await updateAgentMetrics(task.assignedTo);
     await updateSystemMetrics(task.organizationId);
     
     // Trigger analytics recalculation if needed
     await scheduleMetricsUpdate(task.assignedTo);
   }
   
   // When task is assigned
   async function onTaskAssigned(taskId: string, agentId: string) {
     await updateRoutingMetrics(taskId, agentId);
   }
   ```

## 🧪 Testing Requirements

### Unit Tests
- Metrics calculation algorithms
- Analytics engine computations
- Trend analysis functions
- Performance scoring logic

### Integration Tests
- Metrics collection workflows
- API endpoints functionality
- Real-time metrics updates
- Scheduled metrics collection

### Performance Tests
- Large dataset analytics performance
- Metrics collection efficiency
- Database query optimization
- Real-time update latency

## 📝 Acceptance Criteria

- [ ] Agent performance metrics are collected and calculated accurately
- [ ] System-wide metrics track routing efficiency and utilization
- [ ] Analytics engine provides meaningful insights and trends
- [ ] Predictive analytics offer actionable recommendations
- [ ] Real-time metrics update as tasks progress
- [ ] Historical metrics are preserved for trend analysis
- [ ] API endpoints provide comprehensive metrics access
- [ ] Automated metrics collection runs on schedule

## 🔗 Dependencies for Next Tasks

This task enables:
- **Task 7**: Performance dashboards and analytics UI components

## 📋 Deliverables

1. Performance metrics database schema
2. Metrics collection and calculation system
3. Analytics engine with insights generation
4. Automated metrics scheduling
5. API endpoints for metrics and analytics
6. Real-time metrics updates
7. Predictive analytics capabilities
8. Comprehensive unit tests

**Next Task**: [Task 7: Enhanced UI Components](./task7-enhanced-ui.md)
