import { prisma } from './prisma';
import { SkillsManager } from './skills-utils';
import { RulesEngine } from './rules-engine';
import { SLAManager } from './sla-manager';
import { UserStatus } from '@prisma/client';
import {
  TaskWithSkills,
  AgentWithSkills,
  RoutingResult,
  RoutingContext,
  RuleExecutionResult,
  SLAStatus
} from './types';

export class EnhancedTaskRouter {
  private skillsManager: SkillsManager;
  private rulesEngine: RulesEngine;
  private slaManager: SLAManager;

  constructor() {
    this.skillsManager = new SkillsManager();
    this.rulesEngine = new RulesEngine();
    this.slaManager = new SLAManager();
  }

  async routeTask(context: RoutingContext): Promise<RoutingResult> {
    const { task, strategy = 'weighted_round_robin' } = context;

    try {
      // Check SLA status first
      const slaStatus = task.slaPolicyId 
        ? await this.slaManager.checkSLAStatus(task.id)
        : null;

      // Apply routing rules
      const ruleResults = await this.rulesEngine.evaluateRules({
        task,
        organizationId: task.organizationId,
        slaStatus
      });

      // Update task metadata with rule results
      await this.updateTaskWithRuleResults(task.id, ruleResults);

      // Get modified routing strategy
      const modifiedStrategy = this.getModifiedStrategy(ruleResults, slaStatus) || strategy;

      // Get eligible agents considering rules and SLA
      const eligibleAgents = await this.getEligibleAgents(task, ruleResults, slaStatus);
      
      if (eligibleAgents.length === 0) {
        throw new Error('No eligible agents available');
      }

      // Score agents considering SLA
      const agentScores = await Promise.all(
        eligibleAgents.map(async (agent) => ({
          agent,
          score: await this.calculateAgentScore(agent, task, slaStatus),
          reasoning: [`Base agent scoring with SLA considerations`]
        }))
      );

      // Sort by score and get best match
      const sortedScores = agentScores.sort((a, b) => b.score - a.score);
      const selectedScore = sortedScores[0];

      return {
        selectedAgent: selectedScore.agent,
        confidence: selectedScore.score,
        reasoning: selectedScore.reasoning,
        alternativeAgents: sortedScores.slice(1, 4).map(score => score.agent),
        ruleResults,
        appliedStrategy: modifiedStrategy,
        slaStatus
      };

    } catch (error) {
      console.error('Error in routeTask:', error);
      throw error;
    }
  }

  private async updateTaskWithRuleResults(taskId: string, ruleResults: RuleExecutionResult[]): Promise<void> {
    await prisma.task.update({
      where: { id: taskId },
      data: {
        metadata: {
          ruleResults: ruleResults.map(result => ({
            ruleId: result.ruleId,
            ruleName: result.ruleName,
            succeeded: result.succeeded,
            actionsExecuted: result.actionsExecuted
          }))
        }
      }
    });
  }

  private getModifiedStrategy(
    ruleResults: RuleExecutionResult[],
    slaStatus: SLAStatus | null
  ): string | undefined {
    // If SLA is breached or close to breach, prioritize performance-based routing
    if (slaStatus?.breachedSLA || this.isNearingSLABreach(slaStatus)) {
      return 'performance_based';
    }

    // Check for strategy modifications from rules
    const strategyRule = ruleResults.find(result => 
      result.succeeded && 
      result.actionsExecuted?.some(action => action.includes('set_strategy'))
    );

    return strategyRule?.actionsExecuted?.find(action => 
      action.startsWith('set_strategy:')
    )?.split(':')[1];
  }

  private async getEligibleAgents(
    task: TaskWithSkills,
    ruleResults: RuleExecutionResult[],
    slaStatus: SLAStatus | null
  ): Promise<AgentWithSkills[]> {
    // Get base eligible agents
    const dbAgents = await prisma.user.findMany({
      where: {
        organizationId: task.organizationId,
        role: 'AGENT',
        status: UserStatus.AVAILABLE,
        currentTaskCount: {
          lt: prisma.user.fields.maxConcurrentTasks
        }
      },
      include: {
        skills: {
          select: {
            id: true,
            agentId: true,
            skillName: true,
            proficiencyLevel: true,
            createdAt: true
          }
        },
        performance: {
          select: {
            id: true,
            agentId: true,
            avgResponseTime: true,
            avgResolutionTime: true,
            completionRate: true,
            qualityScore: true,
            totalTasksCompleted: true,
            totalTasksAssigned: true,
            lastUpdated: true
          }
        }
      }
    });

    // Transform database results to AgentWithSkills type
    let agents = dbAgents.map(agent => ({
      ...agent,
      skills: agent.skills || [],
      currentTaskCount: agent.currentTaskCount || 0,
      maxConcurrentTasks: agent.maxConcurrentTasks || 5 // Default if not set
    })) as AgentWithSkills[];

    // Apply rule-based filters
    agents = this.applyRuleFilters(agents, ruleResults);

    // Apply SLA-based filters if close to breach
    if (this.isNearingSLABreach(slaStatus)) {
      agents = agents.filter(agent => {
        const performance = agent.performance;
        if (!performance) return false;
        
        // Filter out agents with poor performance metrics
        const minCompletionRate = 0.7; // 70%
        const maxResponseTime = 30; // 30 minutes
        const maxResolutionTime = 240; // 4 hours

        return (
          performance.completionRate >= minCompletionRate &&
          performance.avgResponseTime <= maxResponseTime &&
          performance.avgResolutionTime <= maxResolutionTime
        );
      });
    }

    return agents;
  }

  private applyRuleFilters(
    agents: AgentWithSkills[],
    ruleResults: RuleExecutionResult[]
  ): AgentWithSkills[] {
    let filteredAgents = [...agents];

    for (const result of ruleResults) {
      if (!result.succeeded) continue;

      const performanceRule = result.actionsExecuted?.find(action =>
        action.startsWith('require_min_performance:')
      );

      if (performanceRule) {
        const minScore = parseFloat(performanceRule.split(':')[1]);
        filteredAgents = filteredAgents.filter(agent => {
          const qualityScore = agent.performance?.qualityScore ?? 0;
          return qualityScore >= minScore;
        });
      }
    }

    return filteredAgents;
  }

  private async calculateAgentScore(
    agent: AgentWithSkills,
    task: TaskWithSkills,
    slaStatus: SLAStatus | null
  ): Promise<number> {
    let score = 0;
    
    // Base skill match score (50% weight)
    const skillMatchScore = await this.skillsManager.calculateSkillMatch(
      agent.skills,
      task.requiredSkills
    );
    score += skillMatchScore * 0.5;

    // Workload score (20% weight)
    const workloadScore = 1 - (agent.currentTaskCount / agent.maxConcurrentTasks);
    score += workloadScore * 0.2;

    // Performance score with SLA considerations (30% weight)
    if (agent.performance) {
      const performanceScore = this.calculatePerformanceScore(agent, slaStatus);
      score += performanceScore * 0.3;
    }

    return Math.max(0, Math.min(1, score));
  }

  private calculatePerformanceScore(
    agent: AgentWithSkills,
    slaStatus: SLAStatus | null
  ): number {
    if (!agent.performance) return 0;

    const {
      completionRate,
      avgResponseTime,
      avgResolutionTime,
      qualityScore
    } = agent.performance;

    let score = 0;

    // Base performance metrics
    score += completionRate * 0.3;
    score += qualityScore * 0.3;

    // Response time score (normalized to 0-1, assuming 60 min max)
    const responseScore = Math.max(0, 1 - (avgResponseTime / 60));
    score += responseScore * 0.2;

    // Resolution time score (normalized to 0-1, assuming 8 hours max)
    const resolutionScore = Math.max(0, 1 - (avgResolutionTime / 480));
    score += resolutionScore * 0.2;

    // Apply SLA-based modifiers
    if (slaStatus) {
      if (this.isNearingSLABreach(slaStatus, 'response')) {
        score *= (1 + responseScore);
      }
      if (this.isNearingSLABreach(slaStatus, 'resolution')) {
        score *= (1 + resolutionScore);
      }
    }

    return Math.max(0, Math.min(1, score));
  }

  private isNearingSLABreach(
    slaStatus: SLAStatus | null,
    type: 'response' | 'resolution' | 'both' = 'both'
  ): boolean {
    if (!slaStatus) return false;

    const warningThreshold = 0.25; // 25% of time remaining

    if (type === 'response' || type === 'both') {
      if (slaStatus.responseRemaining !== null) {
        const responseProgress = slaStatus.responseRemaining / (slaStatus.responseRemaining + 1);
        if (responseProgress < warningThreshold) return true;
      }
    }

    if (type === 'resolution' || type === 'both') {
      if (slaStatus.resolutionRemaining !== null) {
        const resolutionProgress = slaStatus.resolutionRemaining / (slaStatus.resolutionRemaining + 1);
        if (resolutionProgress < warningThreshold) return true;
      }
    }

    return false;
  }
}