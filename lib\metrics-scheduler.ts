import { prisma } from './prisma'
import { MetricsCollector } from './metrics-collector'

export class MetricsScheduler {
  private intervals: Map<string, NodeJS.Timeout> = new Map()
  private collector: MetricsCollector

  constructor() {
    this.collector = new MetricsCollector()
  }

  // Start all metric collection schedules
  start(): void {
    // Daily metrics collection at midnight
    this.scheduleDaily('daily-metrics', async () => {
      await this.collectDailyMetrics()
    })
    
    // Weekly metrics collection on Sunday at midnight
    this.scheduleWeekly('weekly-metrics', async () => {
      await this.collectWeeklyMetrics()
    })
    
    // Hourly system metrics
    this.scheduleHourly('system-metrics', async () => {
      await this.collectSystemMetrics()
    })
  }

  // Stop all metric collection schedules
  stop(): void {
    // Convert Map values to array before iteration
    Array.from(this.intervals.values()).forEach(interval => {
      clearInterval(interval)
    })
    this.intervals.clear()
  }

  // Schedule tasks to run at specific intervals
  private scheduleHourly(name: string, task: () => Promise<void>): void {
    // Calculate delay until next hour
    const now = new Date()
    const nextHour = new Date(now)
    nextHour.setHours(nextHour.getHours() + 1, 0, 0, 0)
    const delay = nextHour.getTime() - now.getTime()

    // Run initial task after delay
    const timeout = setTimeout(() => {
      task()
      // Then set up recurring interval
      const interval = setInterval(task, 60 * 60 * 1000) // Every hour
      this.intervals.set(name, interval)
    }, delay)

    this.intervals.set(`${name}-initial`, timeout)
  }

  private scheduleDaily(name: string, task: () => Promise<void>): void {
    // Calculate delay until next midnight
    const now = new Date()
    const nextDay = new Date(now)
    nextDay.setDate(nextDay.getDate() + 1)
    nextDay.setHours(0, 0, 0, 0)
    const delay = nextDay.getTime() - now.getTime()

    // Run initial task after delay
    const timeout = setTimeout(() => {
      task()
      // Then set up recurring interval
      const interval = setInterval(task, 24 * 60 * 60 * 1000) // Every 24 hours
      this.intervals.set(name, interval)
    }, delay)

    this.intervals.set(`${name}-initial`, timeout)
  }

  private scheduleWeekly(name: string, task: () => Promise<void>): void {
    // Calculate delay until next Sunday midnight
    const now = new Date()
    const nextSunday = new Date(now)
    nextSunday.setDate(nextSunday.getDate() + (7 - nextSunday.getDay()))
    nextSunday.setHours(0, 0, 0, 0)
    const delay = nextSunday.getTime() - now.getTime()

    // Run initial task after delay
    const timeout = setTimeout(() => {
      task()
      // Then set up recurring interval
      const interval = setInterval(task, 7 * 24 * 60 * 60 * 1000) // Every 7 days
      this.intervals.set(name, interval)
    }, delay)

    this.intervals.set(`${name}-initial`, timeout)
  }

  // Metric collection tasks
  private async collectDailyMetrics(): Promise<void> {
    try {
      const organizations = await prisma.organization.findMany()
      
      for (const org of organizations) {
        const agents = await prisma.user.findMany({
          where: { 
            organizationId: org.id,
            role: 'AGENT'
          }
        })
        
        for (const agent of agents) {
          await this.collector.collectAgentMetrics(agent.id, 'daily')
        }
      }

      console.log('[MetricsScheduler] Daily metrics collection completed')
    } catch (error) {
      console.error('[MetricsScheduler] Error collecting daily metrics:', error)
    }
  }

  private async collectWeeklyMetrics(): Promise<void> {
    try {
      const organizations = await prisma.organization.findMany()
      
      for (const org of organizations) {
        const agents = await prisma.user.findMany({
          where: { 
            organizationId: org.id,
            role: 'AGENT'
          }
        })
        
        for (const agent of agents) {
          await this.collector.collectAgentMetrics(agent.id, 'weekly')
        }
      }

      console.log('[MetricsScheduler] Weekly metrics collection completed')
    } catch (error) {
      console.error('[MetricsScheduler] Error collecting weekly metrics:', error)
    }
  }

  private async collectSystemMetrics(): Promise<void> {
    try {
      const organizations = await prisma.organization.findMany()
      
      for (const org of organizations) {
        await this.collector.collectSystemMetrics(org.id, 'hourly')
      }

      console.log('[MetricsScheduler] Hourly system metrics collection completed')
    } catch (error) {
      console.error('[MetricsScheduler] Error collecting system metrics:', error)
    }
  }

  // Utility method to run metrics collection immediately
  async collectAllMetrics(): Promise<void> {
    try {
      await Promise.all([
        this.collectDailyMetrics(),
        this.collectWeeklyMetrics(),
        this.collectSystemMetrics()
      ])
      console.log('[MetricsScheduler] Manual metrics collection completed')
    } catch (error) {
      console.error('[MetricsScheduler] Error in manual metrics collection:', error)
      throw error
    }
  }
}